<template>
  <view class="page-wrap">
    <navbar-custom :title="pageTitle" bgColor="#fff" titleColor="#000" />

    <u--form labelPosition="left" labelWidth="auto" :model="form" :rules="rules" ref="uForm">
      <!-- 病例编号：仅编辑模式显示，且为只读 -->
      <view class="partent_box" v-if="isEditMode">
        <u-form-item label="病例编号" prop="caseNumber" borderBottom>
          <u--input
            v-model="form.caseNumber"
            border="none"
            inputAlign="right"
            placeholder="请输入"
            :disabled="true"
            :customStyle="{ color: '#999', backgroundColor: 'transparent' }"
          ></u--input>
        </u-form-item>
      </view>
      <u-line color="#F6F7FA" length="10rpx" margin="10rpx 20rpx"></u-line>
      <list-header title="基础信息"></list-header>
      <view class="partent_box">
        <u-form-item label="姓名" prop="patientName" required borderBottom>
          <u--input v-model="form.patientName" border="none" inputAlign="right" placeholder="请输入"></u--input>
        </u-form-item>
        <u-form-item label="医院名称" prop="hospitalName" required borderBottom>
          <view class="form_wrap" @click="showHospitalPicker">
            <u--input
              v-model="form.hospitalName"
              inputAlign="right"
              :customStyle="{ flex: 1 }"
              placeholder="请选择"
              border="none"
              color="#303133"
            ></u--input>
            <u-icon name="arrow-right" color="#D6D6D6"></u-icon>
          </view>
          <u-picker
            :show="hospshow"
            keyName="hospName"
            closeOnClickOverlay
            title="请选择"
            :columns="[hospitalList]"
            @cancel="hospshow = false"
            @confirm="handleConfirm($event, PICKER_TYPE.HOSPITAL)"
          ></u-picker>
        </u-form-item>
        <u-form-item label="医生" prop="doctorName" required borderBottom>
          <view class="form_wrap" @click="showDoctorPicker">
            <u--input
              v-model="form.doctorName"
              inputAlign="right"
              :customStyle="{ flex: 1 }"
              placeholder="请选择"
              border="none"
              color="#303133"
            ></u--input>
            <u-icon name="arrow-right" color="#D6D6D6"></u-icon>
          </view>
          <u-picker
            :show="docshow"
            keyName="name"
            closeOnClickOverlay
            title="请选择"
            :columns="[docList]"
            @cancel="docshow = false"
            @confirm="handleConfirm($event, PICKER_TYPE.DOCTOR)"
          ></u-picker>
        </u-form-item>
        <u-form-item label="年龄" prop="patientAge" borderBottom required>
          <u--input v-model="form.patientAge" type="number" border="none" inputAlign="right" placeholder="请输入"></u--input>
        </u-form-item>
        <u-form-item label="性别" prop="patientGender" required borderBottom>
          <view class="fileview">
            <u-radio-group v-model="form.patientGender" placement="row" class="item_raido">
              <u-radio name="女" label="女"></u-radio>
              <u-radio name="男" label="男"></u-radio>
            </u-radio-group>
          </view>
        </u-form-item>
        <u-form-item label="出生日期" prop="patientBirthday" required borderBottom>
          <d-pickerdate
            :default-value="defaultStartDate"
            :min-date="new Date(1900, 0, 1).getTime()"
            :max-date="new Date().getTime()"
            v-model="form.patientBirthday"
          ></d-pickerdate>
        </u-form-item>
        <u-form-item label="民族" prop="patientEthnicity" required borderBottom>
          <u--input v-model="form.patientEthnicity" border="none" inputAlign="right" placeholder="请输入"></u--input>
        </u-form-item>
        <u-form-item label="籍贯" prop="patientOrigin" required borderBottom>
          <u--input v-model="form.patientOrigin" border="none" inputAlign="right" placeholder="请输入"></u--input>
        </u-form-item>
        <u-form-item label="联系方式" prop="patientPhone" borderBottom required>
          <u--input
            v-model="form.patientPhone"
            border="none"
            type="number"
            maxlength="11"
            inputAlign="right"
            placeholder="请输入"
          ></u--input>
        </u-form-item>
      </view>
      <u-line color="#F6F7FA" length="10rpx" margin="10rpx 20rpx"></u-line>
      <list-header title="补充信息"></list-header>

      <!-- 动态表单渲染 -->
      <view class="partent_box" v-if="sortedDynamicFields.length > 0">
        <view v-for="field in sortedDynamicFields" :key="`field_${field.id}`" v-if="field && field.id">
          <!-- 输入框 -->
          <u-form-item
            v-if="field.formType === '1'"
            :label="field.labelName"
            :prop="`dynamicFields.field_${field.id}`"
            :required="field.notNull === 1"
            borderBottom
          >
            <u--input
              v-model="form.dynamicFields[`field_${field.id}`]"
              border="none"
              inputAlign="right"
              placeholder="请输入"
              @input="(value) => handleInputChange(field.id, value)"
            ></u--input>
          </u-form-item>

          <!-- 文本框 -->
          <u-form-item
            v-if="field.formType === '2'"
            :label="field.labelName"
            :prop="`dynamicFields.field_${field.id}`"
            :required="field.notNull === 1"
            borderBottom
          >
            <u--textarea
              v-model="form.dynamicFields[`field_${field.id}`]"
              placeholder="请输入"
              :autoHeight="true"
              :rows="4"
              :customStyle="{ backgroundColor: 'transparent' }"
              @input="(value) => handleTextareaChange(field.id, value)"
            ></u--textarea>
          </u-form-item>

          <!-- 单选 -->
          <u-form-item
            v-if="field.formType === '3'"
            :label="field.labelName"
            :prop="`dynamicFields.field_${field.id}`"
            :required="field.notNull === 1"
            borderBottom
          >
            <view class="fileview">
              <u-radio-group
                v-model="form.dynamicFields[`field_${field.id}`]"
                placement="row"
                @change="(value) => handleRadioChange(field.id, value)"
              >
                <u-radio v-for="option in parseFieldOptions(field)" :key="option.name" :name="option.name" :label="option.label"></u-radio>
              </u-radio-group>
            </view>
          </u-form-item>

          <!-- 多选 -->
          <u-form-item
            v-if="field.formType === '4'"
            :label="field.labelName"
            :prop="`dynamicFields.field_${field.id}`"
            :required="field.notNull === 1"
            borderBottom
          >
            <view class="fileview">
              <u-checkbox-group
                v-model="form.dynamicFields[`field_${field.id}`]"
                placement="row"
                @change="(value) => handleCheckboxChange(field.id, value)"
              >
                <u-checkbox
                  v-for="option in parseFieldOptions(field)"
                  :key="option.name"
                  :name="option.name"
                  :label="option.label"
                ></u-checkbox>
              </u-checkbox-group>
            </view>
          </u-form-item>

          <!-- 时间选择器 -->
          <u-form-item
            v-if="field.formType === '5'"
            :label="field.labelName"
            :prop="`dynamicFields.field_${field.id}`"
            :required="field.notNull === 1"
            borderBottom
          >
            <view class="form_wrap" @click="field.id ? showDatePicker(field.id) : null">
              <u--input
                v-model="form.dynamicFields[`field_${field.id}`]"
                inputAlign="right"
                :customStyle="{ flex: 1, backgroundColor: 'transparent' }"
                placeholder="请选择时间"
                border="none"
                color="#303133"
                :disabled="true"
              ></u--input>
              <u-icon name="arrow-right" color="#D6D6D6"></u-icon>
            </view>
          </u-form-item>

          <!-- 地址选择 -->
          <u-form-item
            v-if="field.formType === '6'"
            :label="field.labelName"
            :prop="`dynamicFields.field_${field.id}`"
            :required="field.notNull === 1"
            borderBottom
          >
            <view class="form_wrap" @click="field.id ? showAddressPicker(field.id) : null">
              <u--input
                v-model="form.dynamicFields[`field_${field.id}`]"
                inputAlign="right"
                :customStyle="{ flex: 1, backgroundColor: 'transparent' }"
                placeholder="请选择地址"
                border="none"
                color="#303133"
                :disabled="true"
              ></u--input>
              <u-icon name="arrow-right" color="#D6D6D6"></u-icon>
            </view>
          </u-form-item>

          <!-- 图片上传 -->
          <u-form-item
            v-if="field.formType === '7'"
            :label="field.labelName"
            :prop="`dynamicFields.field_${field.id}`"
            :required="field.notNull === 1"
            borderBottom
          >
            <u-upload
              :fileList="form.dynamicFields[`field_${field.id}`] || []"
              @afterRead="(event) => handleImageUpload(event, field.id)"
              @delete="(event) => handleImageDelete(event, field.id)"
              name="file"
              multiple
              :maxCount="1"
              :previewImage="true"
              accept="image"
              :maxSize="50 * 1024 * 1024"
              @oversize="handleImageOversize"
            ></u-upload>
          </u-form-item>
        </view>
      </view>

      <view class="footer_btn">
        <u-button type="primary" color="#117ACD " text="保存" @click="handleSave(false)" :loading="btnloading" />
        <u-button type="primary" color="#117ACD " text="提交" @click="handleSave(true)" :loading="btnloading" />
      </view>
    </u--form>

    <!-- 疾病种类选择弹窗 -->
    <u-picker
      :show="diseaseTypeShow"
      keyName="name"
      :closeOnClickOverlay="false"
      title="请选择疾病种类"
      :columns="[diseaseTypeList]"
      @cancel="handleDiseaseTypeCancel"
      @confirm="handleDiseaseTypeConfirm"
    ></u-picker>

    <!-- 地址选择弹窗 -->
    <u-picker
      ref="addressPicker"
      :show="addressPickerShow"
      keyName="label"
      closeOnClickOverlay
      title="请选择地址"
      :columns="addressData"
      @cancel="addressPickerShow = false"
      @change="onAddressChange"
      @confirm="onAddressConfirm"
    ></u-picker>

    <!-- 时间选择弹窗 -->
    <u-datetime-picker
      :show="datePickerShow"
      v-model="selectedDate"
      mode="date"
      :minDate="new Date(1900, 0, 1).getTime()"
      :maxDate="new Date(new Date().getFullYear() + 10, 11, 31).getTime()"
      @cancel="datePickerShow = false"
      @confirm="handleDateConfirm"
    ></u-datetime-picker>
  </view>
</template>

<script>
import { getDoctorList, getHospitalAll, getDiseaseType, getChinasTree } from '@/api/common.js'
import { getDiseaseFormConfig, uploadDiseaseImage, addDiseaseInfo, updateDiseaseInfo, getDiseaseInfoDetail } from '@/api/NIPT.js'
export default {
  data() {
    return {
      recordId: '',
      // 医院显示pick
      hospshow: false,
      // 医生显示pick
      docshow: false,
      // 医院picker列表
      hospitalList: [],
      // 医生列表
      docList: [],
      // 疾病种类列表
      diseaseTypeList: [],
      // 疾病种类选择弹窗状态
      diseaseTypeShow: false,
      // 选中的疾病种类
      selectedDiseaseType: null,
      // 动态表单配置
      dynamicFormConfig: {},
      // 地址选择相关
      addressPickerShow: false,
      currentAddressFieldId: null,
      addressData: [],
      addressDataLoaded: false, // 地址数据是否已加载
      cityMap: new Map(), // 城市数据映射
      districtMap: new Map(), // 区县数据映射
      // 时间选择器相关
      datePickerShow: false,
      currentDateFieldId: null,
      selectedDate: new Date().getTime(),
      // 出生日期默认值（设置为30年前）
      defaultStartDate: new Date(new Date().getFullYear() - 30, 0, 1).getTime(),
      // 排序后的动态表单字段（改为响应式数据）
      sortedDynamicFields: [],
      // 按钮加载状态
      btnloading: false,
      // 选择器类型常量
      PICKER_TYPE: {
        HOSPITAL: 1,
        DOCTOR: 2,
      },

      form: {
        // 基础信息
        caseNumber: '',
        patientName: '',
        hospitalName: '',
        hospitalId: '',
        doctorName: '',
        doctorId: '',
        patientAge: '',
        patientGender: '女',
        patientBirthday: '',
        patientEthnicity: '',
        patientOrigin: '',
        patientPhone: '',
        // 动态表单字段
        dynamicFields: {},
        // 动态字段的记录ID（用于更新时传递正确的ID）
        dynamicFieldIds: {},
      },
      rules: {
        // 按页面显示顺序定义验证规则（从上到下）
        // 1. 姓名
        patientName: {
          type: 'string',
          required: true,
          message: '请输入姓名',
          trigger: ['blur'],
        },
        // 2. 医院名称
        hospitalName: {
          type: 'string',
          required: true,
          message: '请选择医院',
          trigger: ['blur', 'change'],
        },
        // 3. 医生
        doctorName: {
          type: 'string',
          required: true,
          message: '请选择医生',
          trigger: ['blur', 'change'],
        },
        // 4. 年龄
        patientAge: {
          type: 'string',
          required: true,
          message: '请输入年龄',
          trigger: ['blur'],
          validator: (_, value, callback) => {
            if (!value) {
              callback(new Error('请输入年龄'))
            } else if (!/^\d+$/.test(value)) {
              callback(new Error('年龄必须为数字'))
            } else if (parseInt(value) < 1 || parseInt(value) > 150) {
              callback(new Error('请输入有效的年龄(1-150)'))
            } else {
              callback()
            }
          },
        },
        // 5. 性别
        patientGender: {
          type: 'string',
          required: true,
          message: '请选择性别',
          trigger: ['blur', 'change'],
        },
        // 6. 出生日期
        patientBirthday: {
          type: 'string',
          required: true,
          message: '请选择出生日期',
          trigger: ['blur', 'change'],
        },
        // 7. 民族
        patientEthnicity: {
          type: 'string',
          required: true,
          message: '请输入民族',
          trigger: ['blur'],
        },
        // 8. 籍贯
        patientOrigin: {
          type: 'string',
          required: true,
          message: '请输入籍贯',
          trigger: ['blur'],
        },
        // 9. 联系方式
        patientPhone: {
          type: 'string',
          required: true,
          message: '请输入联系方式',
          trigger: ['blur'],
          validator: (_, value, callback) => {
            if (!value) {
              callback(new Error('请输入联系方式'))
            } else if (!uni.$u.test.mobile(value)) {
              callback(new Error('请输入正确的手机号码'))
            } else {
              callback()
            }
          },
        },
      },
    }
  },
  computed: {
    // 判断当前是否为编辑模式
    isEditMode() {
      return !!this.recordId
    },
    // 动态页面标题
    pageTitle() {
      return this.isEditMode ? '编辑信息' : '新增信息'
    },
  },
  async onLoad(options) {
    uni.showLoading({
      title: '数据加载',
    })

    // 如果传入了编辑数据，则进入编辑模式
    if (options.recordId) {
      this.recordId = options.recordId

      // 初始化基础数据
      this.updateSortedDynamicFields()
      await this.initData()
      await this.initAddressData()

      // 加载编辑数据
      await this.loadEditData(options.recordId)
    } else {
      // 新增模式
      // 初始化 sortedDynamicFields
      this.updateSortedDynamicFields()

      await this.initData()
      await this.initAddressData()

      // 新增模式下初始化疾病种类
      await this.initDiseaseTypes()

      // 新增模式下，在开发环境填充mock数据
      this.initMockDataInDev()

      uni.hideLoading()
    }
  },
  methods: {
    // 将表单数据转换为API接口格式
    transformFormDataToApiFormat() {
      // 转换动态字段为API要求的格式
      const diseasesTypeTemplateDtlValues = []

      if (this.dynamicFormConfig && this.dynamicFormConfig.diseasesTypeTemplateDtlList) {
        this.dynamicFormConfig.diseasesTypeTemplateDtlList.forEach((field) => {
          if (field && field.id) {
            const fieldKey = `field_${field.id}`
            const fieldValue = this.form.dynamicFields[fieldKey]

            // 只有当字段有值时才添加到数组中
            if (fieldValue !== undefined && fieldValue !== null && fieldValue !== '') {
              let transformedValue = fieldValue

              // 处理不同类型的字段值
              if (field.formType === '4' || field.formType === '7') {
                // 多选和图片上传字段，如果是数组则转为字符串
                if (Array.isArray(fieldValue)) {
                  if (field.formType === '7') {
                    // 图片字段：提取URL数组并转为字符串
                    transformedValue = fieldValue.map((img) => img.url).join(';')
                  } else {
                    // 多选字段：直接join
                    transformedValue = fieldValue.join(';')
                  }
                }
              }

              // 构建动态字段数据
              const dtlValueData = {
                labelValue: transformedValue,
              }

              // 如果是编辑模式且有保存的记录ID，使用记录ID；否则使用模板字段ID
              if (this.isEditMode && this.form.dynamicFieldIds && this.form.dynamicFieldIds[fieldKey]) {
                dtlValueData.id = this.form.dynamicFieldIds[fieldKey]
              } else {
                dtlValueData.id = field.id
              }

              diseasesTypeTemplateDtlValues.push(dtlValueData)
            }
          }
        })
      }

      // 构建API格式的数据
      const apiData = {
        age: this.form.patientAge,
        birthday: this.form.patientBirthday,
        ddtId: this.selectedDiseaseType ? this.selectedDiseaseType.id : null,
        diseasesTypeTemplateDtlValues: diseasesTypeTemplateDtlValues,
        doctId: this.form.doctorId,
        doctName: this.form.doctorName,
        gender: this.form.patientGender,
        hospId: this.form.hospitalId,
        hospName: this.form.hospitalName,
        mobile: this.form.patientPhone,
        patientClan: this.form.patientEthnicity,
        patientPlace: this.form.patientOrigin,
        gname: this.form.patientName,
      }

      // 如果是编辑模式，添加id字段
      if (this.isEditMode && this.recordId) {
        apiData.id = this.recordId
      }

      return apiData
    },

    // 将API数据转换为表单格式
    transformApiDataToFormFormat(apiData) {
      if (!apiData) return

      // 基础字段映射（API格式 → 表单格式）
      this.form.patientAge = apiData.age || ''
      this.form.patientBirthday = apiData.birthday || ''
      this.form.doctorId = apiData.doctId || ''
      this.form.doctorName = apiData.doctName || ''
      this.form.patientGender = apiData.gender || '女'
      this.form.hospitalId = apiData.hospId || ''
      this.form.hospitalName = apiData.hospName || ''
      this.form.patientPhone = apiData.mobile || ''
      this.form.patientEthnicity = apiData.patientClan || ''
      this.form.patientOrigin = apiData.patientPlace || ''
      this.form.patientName = apiData.gname || '' // 修正：使用gname
      this.recordId = apiData.id || ''
      this.form.caseNumber = apiData.pcode || '' // 修正：使用pcode作为病例编号

      // 处理动态字段数据
      if (apiData.diseasesTypeTemplateDtlValueList && Array.isArray(apiData.diseasesTypeTemplateDtlValueList)) {
        // 确保 dynamicFields 和 dynamicFieldIds 对象存在
        if (!this.form.dynamicFields) {
          this.$set(this.form, 'dynamicFields', {})
        }
        if (!this.form.dynamicFieldIds) {
          this.$set(this.form, 'dynamicFieldIds', {})
        }

        apiData.diseasesTypeTemplateDtlValueList.forEach((dtlValue) => {
          if (dtlValue && dtlValue.labelValue !== undefined) {
            // 根据字段类型处理数据格式
            if (this.dynamicFormConfig && this.dynamicFormConfig.diseasesTypeTemplateDtlList) {
              // 尝试通过多种方式匹配字段：
              // 1. 通过dttId匹配
              let fieldConfig = this.dynamicFormConfig.diseasesTypeTemplateDtlList.find((field) => field.id === dtlValue.dttId)

              // 2. 如果dttId匹配不到，尝试通过labelName匹配
              if (!fieldConfig && dtlValue.labelName) {
                fieldConfig = this.dynamicFormConfig.diseasesTypeTemplateDtlList.find((field) => field.labelName === dtlValue.labelName)
              }

              if (fieldConfig) {
                const fieldKey = `field_${fieldConfig.id}`
                let fieldValue = dtlValue.labelValue

                if (fieldConfig.formType === '4') {
                  // 多选字段：字符串转数组
                  fieldValue = fieldValue ? fieldValue.split(';') : []
                } else if (fieldConfig.formType === '7') {
                  // 图片字段：字符串转图片对象数组
                  if (fieldValue) {
                    const urls = fieldValue.split(';')
                    fieldValue = urls.map((url, index) => ({
                      url: url,
                      name: `image_${index + 1}`,
                      size: 0,
                    }))
                  } else {
                    fieldValue = []
                  }
                }

                this.$set(this.form.dynamicFields, fieldKey, fieldValue)
                // 保存字段的记录ID（用于更新时传递）
                this.$set(this.form.dynamicFieldIds, fieldKey, dtlValue.id)
              }
            }
          }
        })
      }

      // 设置疾病类型
      if (apiData.diseaseType) {
        // 根据疾病类型名称找到对应的疾病类型
        const diseaseType = this.diseaseTypeList.find((type) => type.name === apiData.diseaseType)
        if (diseaseType) {
          this.selectedDiseaseType = diseaseType
        }
      }
    },

    // 加载编辑数据
    async loadEditData(recordId) {
      try {
        uni.showLoading({ title: '加载数据中...' })

        // 调用详情接口
        const res = await getDiseaseInfoDetail(recordId)

        if (res && res.code === 200 && res.data) {
          // 先初始化疾病类型列表（编辑模式下不弹出选择器）
          await this.initDiseaseTypes()

          // 先转换基础字段（不包含动态字段）
          this.transformApiDataToFormFormat(res.data)

          // 如果有疾病类型，加载对应的动态表单配置
          if (this.selectedDiseaseType) {
            await this.loadDynamicFormConfig(this.selectedDiseaseType.id)

            // 初始化动态字段默认值
            this.initDynamicFormFields()

            // 现在有了动态表单配置，重新转换动态字段数据
            if (res.data.diseasesTypeTemplateDtlValueList) {
              this.transformApiDataToFormFormat(res.data)
            }
          }

          uni.hideLoading()
        } else {
          uni.hideLoading()
          uni.$u.toast(res?.msg || '获取数据失败')
          // 获取失败时返回上一页
          setTimeout(() => {
            uni.navigateBack()
          }, 1500)
        }
      } catch (error) {
        uni.hideLoading()
        uni.$u.toast('加载数据失败，请重试')
        // 获取失败时返回上一页
        setTimeout(() => {
          uni.navigateBack()
        }, 1500)
      }
    },

    // 开发环境下初始化mock数据
    initMockDataInDev() {
      // 简单的开发环境判断
      // 在uniapp中，可以通过以下方式判断开发环境

      const isDev = process.env.NODE_ENV === 'development' // 如果配置了环境变量

      if (isDev) {
        this.fillMockData()
      }
    },

    // 填充mock数据（也可以手动调用）
    fillMockData() {
      // 生成随机姓名
      const surnames = ['张', '李', '王', '刘', '陈', '杨', '赵', '黄', '周', '吴']
      const names = ['伟', '芳', '娜', '敏', '静', '丽', '强', '磊', '军', '洋', '勇', '艳', '杰', '涛', '明', '超', '秀英', '桂英']
      const randomSurname = surnames[Math.floor(Math.random() * surnames.length)]
      const randomName = names[Math.floor(Math.random() * names.length)]
      const randomPatientName = randomSurname + randomName

      // 填充基础信息mock数据
      this.form = {
        ...this.form,
        // 基础信息
        caseNumber: `CASE${Date.now().toString().slice(-6)}`, // 生成唯一案例号
        patientName: randomPatientName,
        hospitalName: '北京协和医院',
        hospitalId: 'H001',
        doctorName: '李医生',
        doctorId: 'D001',
        patientAge: '28',
        patientGender: '女',
        patientBirthday: '1995-06-15',
        patientEthnicity: '汉族',
        patientOrigin: '北京市',
        patientPhone: '13800138000',
        // 动态表单字段保持空，让用户测试动态表单功能
        dynamicFields: {},
      }

      // 显示提示信息
      uni.showToast({
        title: '已填充开发测试数据',
        icon: 'success',
        duration: 2000,
      })
    },

    // 初始化数据
    async initData() {
      this.hospitalList = []
      try {
        const res = await getHospitalAll()
        uni.hideLoading()
        if (res.code === 200) {
          this.hospitalList = res.data?.length > 0 ? res.data : []
        }
      } catch (error) {
        uni.hideLoading()
        uni.$u.toast('获取医院列表失败')
      }
    },

    // 初始化疾病种类列表
    async initDiseaseTypes() {
      try {
        const res = await getDiseaseType()
        if (res.code === 200) {
          this.diseaseTypeList = res.rows && res.rows.length > 0 ? res.rows : []
          // 新增模式下自动弹出疾病种类选择
          if (!this.isEditMode && this.diseaseTypeList.length > 0) {
            setTimeout(() => {
              this.showDiseaseTypePicker()
            }, 500) // 延迟500ms显示，确保页面加载完成
          }
        } else {
          uni.$u.toast('获取疾病种类失败')
        }
      } catch (error) {
        uni.$u.toast('获取疾病种类失败')
      }
    },

    // 显示疾病种类选择器
    showDiseaseTypePicker() {
      if (this.diseaseTypeList.length === 0) {
        uni.$u.toast('暂无疾病种类数据')
        return
      }
      this.diseaseTypeShow = true
    },

    // 处理疾病种类选择取消（返回上一页）
    handleDiseaseTypeCancel() {
      uni.navigateBack({
        delta: 1,
      })
    },

    // 处理疾病种类选择确认
    async handleDiseaseTypeConfirm(e) {
      this.selectedDiseaseType = e.value[0]
      this.diseaseTypeShow = false

      // 获取动态表单配置
      await this.loadDynamicFormConfig(this.selectedDiseaseType.id)
    },

    // 加载动态表单配置
    async loadDynamicFormConfig(diseaseTypeId) {
      try {
        uni.showLoading({ title: '加载表单配置...' })
        const res = await getDiseaseFormConfig(diseaseTypeId)
        uni.hideLoading()

        if (res.code === 200) {
          // 使用 $set 确保响应式更新
          this.$set(this, 'dynamicFormConfig', res.data)

          // 等待下一个tick确保数据更新
          await this.$nextTick()

          // 手动更新 sortedDynamicFields
          this.updateSortedDynamicFields()

          // 等待字段更新完成
          await this.$nextTick()

          // 初始化动态表单字段
          this.initDynamicFormFields()
          // 生成动态验证规则
          this.generateDynamicRules()
        } else {
          uni.$u.toast('获取表单配置失败')
        }
      } catch (error) {
        uni.hideLoading()
        uni.$u.toast('获取表单配置失败')
      }
    },

    // 初始化动态表单字段
    initDynamicFormFields() {
      if (!this.dynamicFormConfig || !this.dynamicFormConfig.diseasesTypeTemplateDtlList) {
        return
      }

      // 确保 dynamicFields 和 dynamicFieldIds 对象存在
      if (!this.form.dynamicFields) {
        this.$set(this.form, 'dynamicFields', {})
      }
      if (!this.form.dynamicFieldIds) {
        this.$set(this.form, 'dynamicFieldIds', {})
      }

      // 为每个动态字段初始化默认值
      this.dynamicFormConfig.diseasesTypeTemplateDtlList.forEach((field) => {
        if (!field || !field.id) {
          return // 安全检查
        }

        const fieldKey = `field_${field.id}`

        // 根据表单类型设置默认值
        let defaultValue
        switch (field.formType) {
          case '1': // 输入框
          case '2': // 文本框
          case '3': // 单选
          case '5': // 时间选择器
          case '6': // 地址选择
            defaultValue = ''
            break
          case '4': // 多选
          case '7': // 图片上传
            defaultValue = []
            break
          default:
            defaultValue = ''
        }

        // 使用 $set 确保响应式
        this.$set(this.form.dynamicFields, fieldKey, defaultValue)
      })
    },

    // 生成动态验证规则
    generateDynamicRules() {
      if (!this.dynamicFormConfig || !this.dynamicFormConfig.diseasesTypeTemplateDtlList) {
        return
      }

      this.dynamicFormConfig.diseasesTypeTemplateDtlList.forEach((field) => {
        if (!field || !field.id) return // 安全检查
        const fieldKey = `dynamicFields.field_${field.id}`

        if (field.notNull === 1) {
          // 根据字段类型设置不同的验证规则
          let validationRule = {
            required: true,
            trigger: ['blur', 'change'],
          }

          switch (field.formType) {
            case '1': // 输入框
            case '2': // 文本框
            case '5': // 时间选择器
            case '6': // 地址选择
              validationRule.type = 'string'
              validationRule.message = `请填写${field.labelName}`
              break
            case '3': // 单选
              validationRule.type = 'string'
              validationRule.message = `请选择${field.labelName}`
              break
            case '4': // 多选
              validationRule.type = 'array'
              validationRule.min = 1
              validationRule.message = `请至少选择一个${field.labelName}`
              break
            case '7': // 图片上传
              validationRule.type = 'array'
              validationRule.min = 1
              validationRule.message = `请上传${field.labelName}`
              break
            default:
              validationRule.type = 'string'
              validationRule.message = `请填写${field.labelName}`
          }

          this.$set(this.rules, fieldKey, validationRule)
        }
      })
    },

    // 验证动态表单字段（按sort顺序）
    validateDynamicFields() {
      if (!this.dynamicFormConfig || !this.dynamicFormConfig.diseasesTypeTemplateDtlList) {
        return { valid: true, message: '' }
      }

      // 按sort字段排序，确保按页面显示顺序验证
      const sortedFields = this.dynamicFormConfig.diseasesTypeTemplateDtlList
        .filter((field) => field && field.id && field.notNull === 1)
        .slice()
        .sort((a, b) => {
          const sortA = a && typeof a.sort === 'number' ? a.sort : 0
          const sortB = b && typeof b.sort === 'number' ? b.sort : 0
          return sortA - sortB
        })

      for (const field of sortedFields) {
        const fieldKey = `field_${field.id}`
        const fieldValue = this.form.dynamicFields[fieldKey]

        // 检查必填字段是否为空
        if (this.isFieldEmpty(fieldValue, field.formType)) {
          return {
            valid: false,
            message: this.getFieldErrorMessage(field),
          }
        }
      }

      return { valid: true, message: '' }
    },

    // 判断字段是否为空
    isFieldEmpty(value, formType) {
      switch (formType) {
        case '1': // 输入框
        case '2': // 文本框
        case '3': // 单选
        case '5': // 时间选择器
        case '6': // 地址选择
          return !value || value.trim() === ''
        case '4': // 多选
        case '7': // 图片上传
          return !value || !Array.isArray(value) || value.length === 0
        default:
          return !value
      }
    },

    // 获取字段错误信息
    getFieldErrorMessage(field) {
      switch (field.formType) {
        case '1': // 输入框
        case '2': // 文本框
        case '5': // 时间选择器
        case '6': // 地址选择
          return `请填写${field.labelName}`
        case '3': // 单选
          return `请选择${field.labelName}`
        case '4': // 多选
          return `请至少选择一个${field.labelName}`
        case '7': // 图片上传
          return `请上传${field.labelName}`
        default:
          return `请填写${field.labelName}`
      }
    },

    // 更新排序后的动态表单字段
    updateSortedDynamicFields() {
      // 检查 dynamicFormConfig 是否存在且有效
      if (!this.dynamicFormConfig || typeof this.dynamicFormConfig !== 'object') {
        this.$set(this, 'sortedDynamicFields', [])
        return
      }

      // 检查 diseasesTypeTemplateDtlList 是否存在
      if (!this.dynamicFormConfig.diseasesTypeTemplateDtlList) {
        this.$set(this, 'sortedDynamicFields', [])
        return
      }

      const fields = this.dynamicFormConfig.diseasesTypeTemplateDtlList
        .filter((field) => field && field.id)
        .slice()
        .sort((a, b) => {
          // 安全的排序逻辑，处理缺少 sort 属性的情况
          const sortA = a && typeof a.sort === 'number' ? a.sort : 0
          const sortB = b && typeof b.sort === 'number' ? b.sort : 0
          return sortA - sortB
        })

      // 使用 $set 确保响应式更新
      this.$set(this, 'sortedDynamicFields', fields)
    },

    // 处理单选框变化
    handleRadioChange(fieldId, value) {
      const fieldKey = `field_${fieldId}`
      this.$set(this.form.dynamicFields, fieldKey, value)
    },

    // 处理多选框变化
    handleCheckboxChange(fieldId, value) {
      const fieldKey = `field_${fieldId}`
      this.$set(this.form.dynamicFields, fieldKey, value)
    },

    // 处理输入框变化
    handleInputChange(fieldId, value) {
      const fieldKey = `field_${fieldId}`
      this.$set(this.form.dynamicFields, fieldKey, value)
    },

    // 处理文本框变化
    handleTextareaChange(fieldId, value) {
      const fieldKey = `field_${fieldId}`
      this.$set(this.form.dynamicFields, fieldKey, value)
    },

    // 解析字段选项
    parseFieldOptions(field) {
      if (!field || !field.items || typeof field.items !== 'string') {
        return []
      }
      // 将分号分隔的字符串转换为选项数组
      return field.items
        .split(';')
        .filter((item) => item.trim())
        .map((item) => ({
          name: item.trim(),
          label: item.trim(),
        }))
    },

    // 初始化地址数据
    async initAddressData() {
      // 如果已经加载过数据，直接返回
      if (this.addressDataLoaded && this.addressData.length > 0) {
        return
      }

      try {
        uni.showLoading({ title: '加载地址数据...' })
        const res = await getChinasTree()
        uni.hideLoading()
        if (res.length > 0) {
          // 转换API数据格式为u-picker组件需要的格式
          this.addressData = this.transformAddressData(res)
          this.addressDataLoaded = true
        }
      } catch (error) {
        uni.hideLoading()
      }
    },

    // 转换地址数据格式
    transformAddressData(apiData) {
      // 根据实际API返回的数据结构进行转换
      // API数据格式：{id, code, name, pid, level, childList}
      if (!Array.isArray(apiData)) {
        return []
      }

      try {
        // 过滤出省级数据（level = 1）
        const provinceData = apiData.filter((item) => item.level === 1)

        if (provinceData.length === 0) {
          return []
        }

        // 构建省份列表
        const provinces = provinceData.map((province) => ({
          label: province.name,
          value: province.code,
          id: province.id,
        }))

        // 构建城市数据映射（按省份分组）
        const cityMap = new Map()
        provinceData.forEach((province) => {
          if (province.childList && Array.isArray(province.childList)) {
            const cities = province.childList
              .filter((city) => city.level === 2)
              .map((city) => ({
                label: city.name,
                value: city.code,
                id: city.id,
                parentValue: province.code,
              }))
            cityMap.set(province.code, cities)
          }
        })

        // 构建区县数据映射（按城市分组）
        const districtMap = new Map()
        provinceData.forEach((province) => {
          if (province.childList && Array.isArray(province.childList)) {
            province.childList.forEach((city) => {
              if (city.childList && Array.isArray(city.childList)) {
                const districts = city.childList
                  .filter((district) => district.level === 3)
                  .map((district) => ({
                    label: district.name,
                    value: district.code,
                    id: district.id,
                    parentValue: city.code,
                  }))
                districtMap.set(city.code, districts)
              }
            })
          }
        })

        // 为了兼容u-picker组件，我们需要提供初始的城市和区县数据
        // 默认显示第一个省份的城市
        const defaultCities = cityMap.get(provinces[0]?.value) || []

        // 默认显示第一个城市的区县
        const defaultDistricts = defaultCities.length > 0 ? districtMap.get(defaultCities[0]?.value) || [] : []

        // 存储映射关系供后续使用
        this.cityMap = cityMap
        this.districtMap = districtMap

        // 返回三级数据：[省份, 默认城市, 默认区县]
        return [provinces, defaultCities, defaultDistricts]
      } catch (error) {
        return []
      }
    },

    // 地址选择器处理
    async showAddressPicker(fieldId) {
      // 参数验证
      if (!fieldId) {
        uni.showToast({
          title: '字段ID错误',
          icon: 'error',
        })
        return
      }

      // 检查 form.dynamicFields 是否存在
      if (!this.form.dynamicFields) {
        this.form.dynamicFields = {}
      }

      // 确保地址数据已加载
      if (!this.addressDataLoaded || !this.addressData || this.addressData.length === 0) {
        await this.initAddressData()
      }

      this.currentAddressFieldId = fieldId
      this.addressPickerShow = true
    },

    // 地址选择器change事件处理
    onAddressChange(e) {
      // u-picker的change事件参数结构：
      // { value: [选中的对象数组], index: 当前变化项索引, indexs: [各列索引], columnIndex: 变化的列索引 }
      const { value, columnIndex } = e

      // 验证数据有效性
      if (!value || !Array.isArray(value)) {
        return
      }

      // 当选择省份时（第0列变化），更新城市列表
      if (columnIndex === 0) {
        const selectedProvince = value[0]

        if (!selectedProvince || !selectedProvince.value) {
          return
        }

        const provinceCode = selectedProvince.value
        const cities = this.cityMap.get(provinceCode) || []

        // 使用组件的方法更新第二列（城市）
        if (this.$refs.addressPicker && this.$refs.addressPicker.setColumnValues) {
          this.$refs.addressPicker.setColumnValues(1, cities)
        } else {
          // 备选方案：直接更新数据
          this.$set(this.addressData, 1, cities)
        }

        // 如果有城市数据，更新区县列表
        if (cities.length > 0) {
          const firstCityCode = cities[0].value
          const districts = this.districtMap.get(firstCityCode) || []

          // 使用组件的方法更新第三列（区县）
          if (this.$refs.addressPicker && this.$refs.addressPicker.setColumnValues) {
            this.$refs.addressPicker.setColumnValues(2, districts)
          } else {
            // 备选方案：直接更新数据
            this.$set(this.addressData, 2, districts)
          }
        } else {
          // 没有城市数据时清空区县
          if (this.$refs.addressPicker && this.$refs.addressPicker.setColumnValues) {
            this.$refs.addressPicker.setColumnValues(2, [])
          } else {
            this.$set(this.addressData, 2, [])
          }
        }
      }

      // 当选择城市时（第1列变化），更新区县列表
      else if (columnIndex === 1) {
        const selectedCity = value[1]

        if (!selectedCity || !selectedCity.value) {
          return
        }

        const cityCode = selectedCity.value
        const districts = this.districtMap.get(cityCode) || []

        // 使用组件的方法更新第三列（区县）
        if (this.$refs.addressPicker && this.$refs.addressPicker.setColumnValues) {
          this.$refs.addressPicker.setColumnValues(2, districts)
        } else {
          // 备选方案：直接更新数据
          this.$set(this.addressData, 2, districts)
        }
      }
    },

    // 地址选择器确认
    onAddressConfirm(e) {
      if (!this.currentAddressFieldId) {
        return
      }

      // u-picker的confirm事件参数结构：
      // { indexs: [各列索引], value: [选中的对象数组], values: [各列的完整数据] }
      const { value, indexs, values } = e

      // 验证数据有效性
      if (!value || !Array.isArray(value)) {
        uni.showToast({
          title: '地址选择数据无效',
          icon: 'error',
        })
        return
      }

      // 构建地址文本 - 从选中的对象中提取label值
      let addressParts = []

      try {
        // 遍历选中的值，提取label
        value.forEach((item) => {
          if (item && typeof item === 'object') {
            // 优先使用label，如果没有则使用其他可能的字段
            const text = item.label || item.name || item.text || item.title
            if (text) {
              addressParts.push(text)
            }
          } else if (typeof item === 'string' && item.trim()) {
            addressParts.push(item.trim())
          }
        })

        // 如果没有提取到有效的地址部分，尝试从indexs和values中获取
        if (addressParts.length === 0 && indexs && values) {
          indexs.forEach((index, columnIndex) => {
            if (values[columnIndex] && values[columnIndex][index]) {
              const item = values[columnIndex][index]
              const text = item.label || item.name || item.text || item.title || item
              if (text) {
                addressParts.push(text)
              }
            }
          })
        }

        // 过滤掉空值和重复值
        addressParts = addressParts.filter((part, index, arr) => part && part.toString().trim() && arr.indexOf(part) === index)

        const addressText = addressParts.join('-')

        // 验证地址文本
        if (!addressText || addressText.trim() === '') {
          uni.showToast({
            title: '地址格式错误，请重新选择',
            icon: 'error',
          })
          return
        }

        // 更新表单数据
        const fieldKey = `field_${this.currentAddressFieldId}`
        this.$set(this.form.dynamicFields, fieldKey, addressText)
      } catch (error) {
        uni.showToast({
          title: '地址处理失败',
          icon: 'error',
        })
        return
      }

      // 关闭选择器
      this.addressPickerShow = false
      this.currentAddressFieldId = null
    },

    // 时间选择器处理
    showDatePicker(fieldId) {
      // 参数验证
      if (!fieldId) {
        uni.showToast({
          title: '字段ID错误',
          icon: 'error',
        })
        return
      }

      // 检查 form.dynamicFields 是否存在
      if (!this.form.dynamicFields) {
        this.form.dynamicFields = {}
      }

      this.currentDateFieldId = fieldId
      const fieldKey = `field_${fieldId}`

      // 如果已有值，设置为当前值，否则设置为今天
      const currentValue = this.form.dynamicFields[fieldKey]

      try {
        if (currentValue && currentValue.trim() !== '') {
          // 尝试解析现有日期
          const parsedDate = new Date(currentValue)
          if (!isNaN(parsedDate.getTime())) {
            this.selectedDate = parsedDate.getTime()
          } else {
            this.selectedDate = new Date().getTime()
          }
        } else {
          this.selectedDate = new Date().getTime()
        }
      } catch (error) {
        this.selectedDate = new Date().getTime()
      }

      this.datePickerShow = true
    },

    // 处理时间选择确认
    handleDateConfirm(e) {
      if (!this.currentDateFieldId) {
        return
      }

      // 验证事件对象和值
      if (!e || (e.value === undefined && e === undefined)) {
        uni.showToast({
          title: '时间选择错误',
          icon: 'error',
        })
        return
      }

      const fieldKey = `field_${this.currentDateFieldId}`

      try {
        // 获取时间值，兼容不同的事件格式
        let dateValue = e.value || e || this.selectedDate

        // 格式化日期为 YYYY-MM-DD 格式
        const date = new Date(dateValue)

        // 验证日期是否有效
        if (isNaN(date.getTime())) {
          uni.showToast({
            title: '无效的日期',
            icon: 'error',
          })
          return
        }

        const formattedDate =
          date.getFullYear() + '-' + String(date.getMonth() + 1).padStart(2, '0') + '-' + String(date.getDate()).padStart(2, '0')

        // 确保 form.dynamicFields 存在
        if (!this.form.dynamicFields) {
          this.form.dynamicFields = {}
        }

        // 保存到对应的动态字段
        this.$set(this.form.dynamicFields, fieldKey, formattedDate)
      } catch (error) {
        uni.showToast({
          title: '时间处理失败',
          icon: 'error',
        })
        return
      }

      // 关闭选择器
      this.datePickerShow = false
      this.currentDateFieldId = null
    },

    // 图片上传处理
    async handleImageUpload(event, fieldId) {
      const fieldKey = `field_${fieldId}`
      if (!this.form.dynamicFields[fieldKey]) {
        this.$set(this.form.dynamicFields, fieldKey, [])
      }

      // 处理上传的图片
      // 兼容不同的事件结构
      let file = event.file || event
      if (Array.isArray(file)) {
        file = file[0]
      }

      if (!file || !file.url) {
        uni.$u.toast('图片选择失败，请重试')
        return
      }

      // 显示上传中提示
      uni.showLoading({
        title: '图片上传中...',
        mask: true,
      })

      try {
        // 调用上传接口
        const uploadResult = await uploadDiseaseImage(file.url)
        uni.hideLoading()

        if (uploadResult && uploadResult.code === 200) {
          // 上传成功，保存服务器返回的图片信息
          this.form.dynamicFields[fieldKey].push({
            url: uploadResult.url,
            name: uploadResult.fileName,
          })
          uni.$u.toast('图片上传成功')
        } else {
          // 上传失败
          uni.$u.toast(uploadResult?.msg || '图片上传失败，请重试')
        }
      } catch (error) {
        uni.hideLoading()
        uni.$u.toast('图片上传失败，请检查网络连接')
      }
    },

    // 图片删除处理
    handleImageDelete(event, fieldId) {
      const fieldKey = `field_${fieldId}`
      if (this.form.dynamicFields[fieldKey]) {
        // 从本地数组中删除
        this.form.dynamicFields[fieldKey].splice(event.index, 1)
        uni.$u.toast('图片已删除')
      }
    },

    // 图片大小超限处理
    handleImageOversize() {
      uni.$u.toast('图片大小不能超过10MB')
    },

    // 加载医生列表
    async loadDoctorList(hospitalName) {
      try {
        const res = await getDoctorList({ hospName: hospitalName })
        if (res.code === 200) {
          this.docList = res.data.list && res.data.list.length > 0 ? res.data.list : []
          return true
        } else {
          return false
        }
      } catch (error) {
        uni.$u.toast('获取医生列表失败')
        return false
      }
    },

    // 处理表单提交响应
    handleFormSubmitResponse(res, successMessage) {
      uni.hideLoading()
      this.btnloading = false

      if (res.code === 200) {
        uni.$u.toast(successMessage)
        this.navigateBackWithRefresh()
      } else {
        uni.$u.toast(res.msg)
      }
    },

    // 页面回退并刷新上一页
    navigateBackWithRefresh() {
      setTimeout(() => {
        const pages = getCurrentPages()
        const prevPage = pages[pages.length - 2]
        uni.navigateBack({ delta: 1 })
        if (prevPage && prevPage.$vm && prevPage.$vm.isreset) {
          prevPage.$vm.isreset()
        }
      })
    },
    // 选择器确认处理
    async handleConfirm(e, pickerType) {
      if (pickerType === this.PICKER_TYPE.HOSPITAL) {
        // 处理医院选择
        const selectedHospital = e.value[0]
        this.form.hospitalName = selectedHospital.hospName
        this.form.hospitalId = selectedHospital.id

        // 重置医生相关数据
        this.docList = []
        this.form.doctorName = ''
        this.form.doctorId = ''

        // 加载对应医院的医生列表
        await this.loadDoctorList(this.form.hospitalName)
      } else if (pickerType === this.PICKER_TYPE.DOCTOR) {
        // 处理医生选择
        const selectedDoctor = e.value[0]
        this.form.doctorName = selectedDoctor.name
        this.form.doctorId = selectedDoctor.id
      }

      // 关闭所有选择器
      this.docshow = false
      this.hospshow = false

      // 验证相关字段
      try {
        const fieldsToValidate = pickerType === this.PICKER_TYPE.HOSPITAL ? ['hospitalName'] : ['hospitalName', 'doctorName']
        await this.$refs.uForm.validateField(fieldsToValidate)
      } catch (error) {
        // 验证失败，忽略错误
      }
    },
    // 显示医院选择器
    showHospitalPicker() {
      this.hospshow = true
    },

    // 显示医生选择器
    async showDoctorPicker() {
      if (!this.form.hospitalName) {
        uni.$u.toast('请先选择医院！')
        return
      }

      // 如果医生列表为空，先加载
      if (this.docList.length === 0) {
        const success = await this.loadDoctorList(this.form.hospitalName)
        if (!success) {
          return // 加载失败，不显示选择器
        }
      }

      this.docshow = true
    },

    // 按页面顺序验证基础信息字段
    async validateBasicFields() {
      // 按照页面从上到下的顺序验证基础信息字段
      const basicFieldsOrder = [
        'patientName', // 姓名
        'hospitalName', // 医院名称
        'doctorName', // 医生
        'patientAge', // 年龄
        'patientGender', // 性别
        'patientBirthday', // 出生日期
        'patientEthnicity', // 民族
        'patientOrigin', // 籍贯
        'patientPhone', // 联系方式
      ]

      // 按顺序逐个验证字段
      for (const fieldName of basicFieldsOrder) {
        try {
          await this.$refs.uForm.validateField(fieldName)
        } catch (error) {
          // 验证失败时抛出错误，中断后续验证
          throw error
        }
      }
    },

    // 保存/更新表单
    async handleSave(status = false) {
      try {
        // 按从上到下的顺序验证表单
        // 1. 先验证基础信息字段（按页面显示顺序）
        await this.validateBasicFields()

        // 2. 再验证动态表单字段（补充信息）
        const dynamicValidation = this.validateDynamicFields()
        if (!dynamicValidation.valid) {
          uni.$u.toast(dynamicValidation.message)
          return
        }

        uni.showLoading({
          title: this.isEditMode ? '更新中' : '保存中',
        })
        this.btnloading = true

        if (this.isEditMode) {
          await this.submitEditForm(status)
        } else {
          await this.submitNewForm(status)
        }
      } catch (error) {
        // 表单验证失败
        // 显示验证失败的具体信息
        if (error && error.length > 0) {
          // 如果有具体的验证错误信息
          const firstError = error[0]
          if (firstError && firstError.message) {
            uni.$u.toast(firstError.message)
          } else {
            uni.$u.toast('请检查表单信息是否完整')
          }
        } else {
          // 通用验证失败提示
          uni.$u.toast('请检查表单信息是否完整')
        }
      }
    },

    // 提交新增表单
    async submitNewForm(status) {
      try {
        // 转换表单数据为API格式
        const apiData = this.transformFormDataToApiFormat()
        const res = await addDiseaseInfo({ ...apiData, status })
        this.handleFormSubmitResponse(res, '新增成功！')
      } catch (error) {
        uni.hideLoading()
        this.btnloading = false
        uni.$u.toast('新增失败，请重试')
      }
    },

    // 提交编辑表单
    async submitEditForm(status) {
      try {
        // 转换表单数据为API格式
        const apiData = this.transformFormDataToApiFormat()
        const res = await updateDiseaseInfo({ ...apiData, status })
        this.handleFormSubmitResponse(res, '更新成功！')
      } catch (error) {
        uni.hideLoading()
        this.btnloading = false
        uni.$u.toast('更新失败，请重试')
      }
    },
  },
  onReady() {
    //如果需要兼容微信小程序，并且校验规则中含有方法等，只能通过setRules方法设置规则。
    this.$refs.uForm.setRules(this.rules)
  },
}
</script>

<style scoped lang="scss">
.page-wrap {
  background: #fff;
  padding: 20rpx 30rpx;
  overflow-y: scroll;
}

.partent_box {
  padding: 0rpx 20rpx;
}

.form_wrap {
  display: flex;
  align-items: center;
}

.fileview {
  display: flex;
  align-items: center;
  justify-content: flex-end;

  ::v-deep .u-radio-group {
    justify-content: flex-end;

    .u-radio {
      margin-left: 38rpx;
    }
  }

  ::v-deep .u-checkbox-group {
    justify-content: flex-end;
    flex-wrap: wrap;

    .u-checkbox {
      margin-left: 38rpx;
      margin-bottom: 20rpx;
    }
  }
}

.footer_btn {
  margin: 20rpx 0rpx;
}
</style>
