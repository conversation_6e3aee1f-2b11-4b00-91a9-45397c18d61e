<!-- 找回密码 -->
<template>
  <view class="login-container retrieve-container">
    <view class="costom-back-view" @click="goBack">
      <image src="/static/common/assets/arrow-left.png" class="back-icon"></image>
      <text>返回</text>
    </view>

    <view class="logo">
      <image class="logo-img" src="/static/common/assets/login_bg.png"></image>
      <view class="logo-wrap">
        <h2 class="title-zh">结构畸形防治协作网络信息平台</h2>
        <h3 class="title-en">Medjcal applet</h3>
      </view>
    </view>
    <h1 v-if="retrieveStatus !== 'success'" class="login-title">{{ retrieveTitle }}</h1>

    <view class="login-form-wrap">
      <template v-if="retrieveStatus === 'confirmPhone'">
        <view class="form-item">
          <u--input
            v-model="retrieveForm.mobile"
            prefixIcon="/static/common/assets/login-phone.png"
            prefixIconStyle="width: 25rpx; height: 27rpx;"
            fontSize="26rpx"
            shape="circle"
            type="number"
            maxlength="11"
            placeholder="请输入注册手机号码"
          ></u--input>
        </view>
        <view class="form-item">
          <u-input
            v-model="retrieveForm.smscode"
            prefixIcon="/static/common/assets/login-message.png"
            prefixIconStyle="width: 26rpx; height: 23rpx;"
            fontSize="26rpx"
            shape="circle"
            type="number"
            placeholder="请输入短信验证码"
          >
            <template slot="suffix">
              <u-code ref="uCode" seconds="60" changeText="X秒重新获取" @change="codeChange"></u-code>
              <u-button :text="tips" color="#105EE4" shape="circle" size="mini" @click="getCode"></u-button> </template
          ></u-input>
        </view>

        <view class="submit-wrap">
          <u-button text="下一步" shape="circle" color="#105EE4" class="login-submit" @click="nextStep"></u-button>
        </view>
      </template>

      <template v-else-if="retrieveStatus === 'setUpPwd'">
        <view class="form-item">
          <u--input
            v-model="setUpForm.password"
            prefixIcon="/static/common/assets/login-pwd.png"
            prefixIconStyle="width: 25rpx; height: 27rpx;"
            fontSize="26rpx"
            shape="circle"
            placeholder="请设置6-20位登录密码"
            clearable
          ></u--input>
        </view>
        <view class="form-item">
          <u--input
            v-model="setUpForm.topass"
            prefixIcon="/static/common/assets/login-pwd.png"
            prefixIconStyle="width: 25rpx; height: 27rpx;"
            fontSize="26rpx"
            shape="circle"
            placeholder="请再次确认登录密码"
            clearable
          ></u--input>
        </view>

        <view class="submit-wrap">
          <u-button text="确定" shape="circle" color="#105EE4" class="login-submit" :loading="subloading" @click="handleSubmit"></u-button>
        </view>
      </template>

      <template v-else-if="retrieveStatus === 'success'">
        <view class="success-view">
          <image class="success-img" src="/static/common/assets/confirm-success.png"></image>
          <view class="success-text">密码设置成功</view>
        </view>
        <view class="submit-wrap">
          <u-button text="立即登录" shape="circle" color="#105EE4" class="login-submit" @click="goBack"></u-button>
        </view>
      </template>
    </view>
  </view>
</template>

<script>
import { phone } from '@/util/reg.js'
export default {
  data() {
    return {
      retrieveTitle: '找回密码',
      retrieveStatus: 'confirmPhone', // 找回密码的状态： confirmPhone -- 验证手机号； setUpPwd -- 设置密码； success -- 设置成功
      tips: '',
      retrieveForm: {
        mobile: '',
        smscode: '',
      },
      setUpForm: {
        sign: '',
        password: '',
        topass: '',
      },
      subloading: false,
    }
  },
  methods: {
    codeChange(text) {
      this.tips = text
    },
    getCode() {
      let { mobile } = this.retrieveForm
      if (!phone.test(mobile)) {
        uni.$u.toast('请输入正确的手机号！')
        return
      }
      if (this.$refs.uCode.canGetCode) {
        // 模拟向后端请求验证码
        uni.showLoading({
          title: '正在获取验证码',
        })
        this.$post('auth/retrievepasswordsms', {
          mobile,
        }).then((res) => {
          console.log(res, '==')
          if (res.code === 200) {
            uni.hideLoading()
            // 这里此提示会被this.start()方法中的提示覆盖
            uni.$u.toast('验证码已发送')
            // 通知验证码组件内部开始倒计时
            this.$refs.uCode.start()
          } else {
            uni.$u.toast(res.msg)
          }
        })
      } else {
        uni.$u.toast('倒计时结束后再发送')
      }
    },
    nextStep() {
      let { mobile, smscode } = this.retrieveForm
      if (!phone.test(mobile)) {
        uni.$u.toast('请输入正确的手机号！')
        return
      }
      if (!smscode) {
        uni.$u.toast('短信验证码不能为空！')
        return
      }
      // 找回密码， 验证码是否正确
      this.$post('auth/retrievepasswordsmsverify', {
        mobile,
        smscode,
      }).then((res) => {
        if (res.code === 200) {
          this.setUpForm.sign = res.data.sign
          this.retrieveStatus = 'setUpPwd'
        } else {
          uni.$u.toast(res.msg)
        }
      })
    },
    handleSubmit() {
      const mobile = this.retrieveForm.mobile
      const sign = this.setUpForm.sign
      let { password, topass } = this.setUpForm
      if (!password) {
        uni.$u.toast('密码不能为空！')
        return
      } else if (password.length < 6 || password.length > 20) {
        uni.$u.toast('请输入6-20位登录密码！')
        return
      }
      if (!topass) {
        uni.$u.toast('确认密码不能为空！')
        return
      } else if (password !== topass) {
        uni.$u.toast('密码和确认密码不一致！')
        return
      }
      this.subloading = true
      this.$post('auth/resetpasswold', {
        mobile,
        sign,
        password,
      }).then((res) => {
        if (res == undefined) {
          this.subloading = false
        } else {
          if (res.code === 200) {
            this.retrieveStatus = 'success'
          } else {
            uni.$u.toast(res.msg)
          }
          this.subloading = false
        }
      })
    },
    goBack() {
      uni.navigateBack()
    },
    // goLogin() {
    // 	uni.redirectTo({
    // 		url: "/pages/login/index",
    // 	});
    // }
  },
}
</script>

<style lang="scss">
.retrieve-container {
  position: relative;

  .go-back-icon {
    position: absolute;
    top: 64rpx;
    left: 24rpx;
    z-index: 1000;
  }

  .success-view {
    text-align: center;

    .success-img {
      width: 161rpx;
      height: 161rpx;
    }

    .success-text {
      font-size: 30rpx;
      margin-top: 40rpx;
    }
  }
}
</style>
