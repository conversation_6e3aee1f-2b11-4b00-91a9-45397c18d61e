<template>
    <div class="app-container">
        <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="100px">
            <el-form-item label="专家组名称" prop="name">
                <el-input v-model="queryParams.name" placeholder="请输入专家组名称" clearable @keyup.enter.native="handleQuery" />
            </el-form-item>
            <el-form-item label="擅长介绍" prop="adept">
                <el-input v-model="queryParams.adept" placeholder="请输入擅长介绍" clearable @keyup.enter.native="handleQuery" />
            </el-form-item>
            <el-form-item>
                <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
                <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
            </el-form-item>
        </el-form>

        <el-row :gutter="10" class="mb8">
            <el-col :span="1.5">
                <el-button type="primary" plain icon="el-icon-plus" size="mini" @click="handleAdd" v-hasPermi="['system:group:add']">新增</el-button>
            </el-col>
            <el-col :span="1.5">
                <el-button type="danger" plain icon="el-icon-delete" size="mini" :disabled="multiple" @click="handleDelete" v-hasPermi="['system:group:remove']">删除</el-button>
            </el-col>
            <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
        </el-row>

        <el-table v-loading="loading" :data="groupList" @selection-change="handleSelectionChange">
            <el-table-column type="selection" width="55" align="center" />
            <el-table-column label="id" align="center" prop="id" />
            <el-table-column label="专家组名称" align="center" prop="name" />
            <el-table-column label="擅长介绍" align="center" prop="adept" />
            <el-table-column label="状态" align="center" prop="status">
                <template slot-scope="scope">
                    <el-tag :type="scope.row.status == 1 ? 'success' : 'danger'">
                        {{ scope.row.status == 1 ? '启用' : '停用' }}
                    </el-tag>
                </template>
            </el-table-column>
            <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
                <template slot-scope="scope">
                    <el-button size="mini" type="text" icon="el-icon-edit" @click="handleUpdate(scope.row)" v-hasPermi="['system:group:edit']">修改</el-button>
                    <el-button size="mini" type="text" :icon="scope.row.status == 1 ? 'el-icon-close' : 'el-icon-check'" @click="handleStatusChange(scope.row)" v-hasPermi="['system:group:edit']">
                        {{ scope.row.status == 1 ? '停用' : '启用' }}
                    </el-button>
                    <el-button size="mini" type="text" icon="el-icon-delete" @click="handleDelete(scope.row)" v-hasPermi="['system:group:remove']">删除</el-button>
                </template>
            </el-table-column>
        </el-table>

        <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNum" :limit.sync="queryParams.pageSize" @pagination="getList" />

        <!-- 添加或修改专家组管理对话框 -->
        <el-dialog :title="title" :visible.sync="open" width="500px" append-to-body>
            <el-form ref="form" :model="form" :rules="rules" label-width="100px">
                <el-form-item label="专家组名称" prop="name">
                    <el-input v-model="form.name" placeholder="请输入专家组名称" />
                </el-form-item>
                <el-form-item label="选择专家" prop="selectedDoctors">
                    <el-button type="primary" icon="el-icon-plus" size="mini" @click="handleSelectDoctor">选择专家</el-button>
                    <div v-if="form.selectedDoctors && form.selectedDoctors.length > 0" style="margin-top: 10px" :key="form.selectedDoctors.length">
                        <el-tag v-for="doctor in form.selectedDoctors" :key="`doctor-${doctor.id}`" closable @close="() => handleRemoveDoctor(doctor)" style="margin-right: 8px; margin-bottom: 8px"> {{ doctor.name }} </el-tag>
                    </div>
                    <div v-else style="margin-top: 10px; color: #999; font-size: 12px">请选择专家，支持选择多个专家</div>
                </el-form-item>
                <el-form-item label="擅长介绍" prop="adept">
                    <el-input v-model="form.adept" type="textarea" placeholder="请输入擅长介绍" />
                </el-form-item>
                <el-form-item label="备注" prop="remarks">
                    <el-input v-model="form.remarks" type="textarea" placeholder="请输入内容" />
                </el-form-item>
            </el-form>
            <div slot="footer" class="dialog-footer">
                <el-button type="primary" @click="submitForm">确 定</el-button>
                <el-button @click="cancel">取 消</el-button>
            </div>
        </el-dialog>

        <!-- 选择医生对话框 -->
        <select-doctor ref="selectDoctor" @confirm="handleDoctorSelected" />
    </div>
</template>

<script>
import { listGroup, getGroup, delGroup, addGroup, updateGroup, updateGroupStatus } from '@/api/system/group';
import SelectDoctor from './selectDoctor';

export default {
    name: 'Group',
    components: {
        SelectDoctor,
    },
    data() {
        return {
            // 遮罩层
            loading: true,
            // 选中数组
            ids: [],
            // 非单个禁用
            single: true,
            // 非多个禁用
            multiple: true,
            // 显示搜索条件
            showSearch: true,
            // 总条数
            total: 0,
            // 专家组管理表格数据
            groupList: [],
            // 弹出层标题
            title: '',
            // 是否显示弹出层
            open: false,
            // 查询参数
            queryParams: {
                pageNum: 1,
                pageSize: 10,
                name: null,
                adept: null,
            },
            // 表单参数
            form: {
                selectedDoctors: [], // 确保初始化时就有这个字段
            },
            // 表单校验
            rules: {
                name: [{ required: true, message: '专家组名称不能为空', trigger: 'blur' }],
                adept: [{ required: true, message: '擅长介绍不能为空', trigger: 'blur' }],
                selectedDoctors: [{ required: true, message: '请至少选择一位专家', trigger: 'change' }],
            },
        };
    },
    created() {
        this.getList();
    },
    methods: {
        /** 查询专家组管理列表 */
        getList() {
            this.loading = true;
            listGroup(this.queryParams).then((response) => {
                this.groupList = response.rows;
                this.total = response.total;
                this.loading = false;
            });
        },
        // 取消按钮
        cancel() {
            this.open = false;
            this.reset();
        },
        // 表单重置
        reset() {
            this.form = {
                id: null,
                name: null,
                adept: null,
                remarks: null,
                status: 1, // 默认状态为启用
                selectedDoctors: [], // 添加选中的医生列表
            };
            this.resetForm('form');
        },
        /** 搜索按钮操作 */
        handleQuery() {
            this.queryParams.pageNum = 1;
            this.getList();
        },
        /** 重置按钮操作 */
        resetQuery() {
            this.resetForm('queryForm');
            this.handleQuery();
        },
        // 多选框选中数据
        handleSelectionChange(selection) {
            this.ids = selection.map((item) => item.id);
            this.single = selection.length !== 1;
            this.multiple = !selection.length;
        },
        /** 新增按钮操作 */
        handleAdd() {
            this.reset();
            this.open = true;
            this.title = '添加专家组管理';
        },
        /** 修改按钮操作 */
        handleUpdate(row) {
            this.reset();
            const id = row.id || this.ids;
            getGroup(id).then((response) => {
                this.form = response.data;
                // 如果后端返回的数据中包含医生列表，则设置到selectedDoctors中
                // 后端返回的数据结构中包含doctorList字段
                if (response.data.doctorList && Array.isArray(response.data.doctorList)) {
                    this.form.selectedDoctors = response.data.doctorList;
                } else {
                    this.form.selectedDoctors = [];
                }
                this.open = true;
                this.title = '修改专家组管理';
            });
        },
        /** 提交按钮 */
        submitForm() {
            this.$refs['form'].validate((valid) => {
                if (valid) {
                    // 准备提交的数据
                    const submitData = {
                        ...this.form,
                        // 将选中的医生转换为医生ID数组
                        doctorIds: this.form.selectedDoctors.map((doctor) => doctor.id),
                    };
                    // 移除selectedDoctors字段，避免提交不必要的数据
                    delete submitData.selectedDoctors;

                    if (this.form.id != null) {
                        updateGroup(submitData).then((response) => {
                            this.$modal.msgSuccess('修改成功');
                            this.open = false;
                            this.getList();
                        });
                    } else {
                        addGroup(submitData).then((response) => {
                            this.$modal.msgSuccess('新增成功');
                            this.open = false;
                            this.getList();
                        });
                    }
                }
            });
        },
        /** 删除按钮操作 */
        handleDelete(row) {
            const ids = row.id || this.ids;
            this.$modal
                .confirm('是否确认删除专家组管理编号为"' + ids + '"的数据项？')
                .then(function () {
                    return delGroup(ids);
                })
                .then(() => {
                    this.getList();
                    this.$modal.msgSuccess('删除成功');
                })
                .catch(() => {});
        },
        /** 选择医生按钮操作 */
        handleSelectDoctor() {
            console.log('handleSelectDoctor called, current selectedDoctors:', this.form.selectedDoctors);
            // 传递当前已选择的医生列表
            this.$refs.selectDoctor.show(this.form.selectedDoctors || []);
        },
        /** 医生选择确认 */
        handleDoctorSelected(selectedDoctors) {
            console.log('handleDoctorSelected called with:', selectedDoctors);
            console.log('selectedDoctors length:', selectedDoctors.length);
            console.log('Current form.selectedDoctors before merge:', this.form.selectedDoctors);

            // selectedDoctors 已经包含了所有选择的医生（包括之前选择的和新选择的）
            // 因为在 selectDoctor 组件中已经处理了合并逻辑
            this.form.selectedDoctors = [...selectedDoctors];

            console.log('After setting, form.selectedDoctors:', this.form.selectedDoctors);
            console.log('form.selectedDoctors length:', this.form.selectedDoctors.length);

            // 触发表单验证
            this.$refs.form.validateField('selectedDoctors');
        },
        /** 移除单个医生 */
        handleRemoveDoctor(doctor) {
            console.log('handleRemoveDoctor called for doctor:', doctor);
            console.log('Current selectedDoctors before removal:', this.form.selectedDoctors);
            console.log('Doctor to remove:', doctor);

            // 创建新的 form 对象来确保响应性
            const newForm = { ...this.form };
            newForm.selectedDoctors = this.form.selectedDoctors.filter((item) => item.id !== doctor.id);
            this.form = newForm;

            console.log('selectedDoctors after removal:', this.form.selectedDoctors);
            console.log('selectedDoctors length:', this.form.selectedDoctors.length);

            // 触发表单验证
            this.$nextTick(() => {
                if (this.$refs.form) {
                    this.$refs.form.validateField('selectedDoctors');
                }
            });
        },
        /** 专家组状态修改 */
        handleStatusChange(row) {
            const text = row.status === 1 ? '停用' : '启用';
            this.$modal
                .confirm('确认要"' + text + '""' + row.name + '"专家组吗？')
                .then(() => {
                    // 只传递ID和状态参数
                    const updateData = {
                        id: row.id,
                        status: row.status === 1 ? 0 : 1, // 切换状态
                    };
                    return updateGroupStatus(updateData);
                })
                .then(() => {
                    this.$modal.msgSuccess(text + '成功');
                    this.getList();
                })
                .catch(() => {
                    // 如果操作失败，不需要回滚，因为我们没有预先修改数据
                });
        },
    },
};
</script>
