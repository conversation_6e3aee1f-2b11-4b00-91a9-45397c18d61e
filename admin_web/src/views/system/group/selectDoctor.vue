<template>
    <!-- 选择医生 -->
    <el-dialog title="选择专家" :visible.sync="visible" width="800px" top="5vh" append-to-body>
        <el-form :model="queryParams" ref="queryForm" size="small" :inline="true">
            <el-form-item label="医生姓名" prop="name">
                <el-input v-model="queryParams.name" placeholder="请输入医生姓名" clearable @keyup.enter.native="handleQuery" />
            </el-form-item>
            <el-form-item label="所属医院" prop="hospName">
                <el-input v-model="queryParams.hospName" placeholder="请输入所属医院" clearable @keyup.enter.native="handleQuery" />
            </el-form-item>
            <el-form-item>
                <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
                <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
            </el-form-item>
        </el-form>
        <el-row>
            <el-table @row-click="clickRow" ref="table" :data="doctorList" @selection-change="handleSelectionChange" height="260px" v-loading="loading">
                <el-table-column type="selection" width="55" align="center"></el-table-column>
                <el-table-column label="医生姓名" prop="name" :show-overflow-tooltip="true" />
                <el-table-column label="性别" prop="gender" :show-overflow-tooltip="true" />
                <el-table-column label="所属医院" prop="hospName" :show-overflow-tooltip="true" />
                <el-table-column label="科室名称" prop="depName" :show-overflow-tooltip="true" />
                <el-table-column label="职级" prop="job" :show-overflow-tooltip="true" />
                <el-table-column label="手机号" prop="mobile" :show-overflow-tooltip="true" />
            </el-table>
        </el-row>
        <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNum" :limit.sync="queryParams.pageSize" @pagination="getList" />
        <div slot="footer" class="dialog-footer">
            <el-button type="primary" @click="handleSelectDoctor">确 定</el-button>
            <el-button @click="cancel">取 消</el-button>
        </div>
    </el-dialog>
</template>

<script>
import { listDoctor } from '@/api/system/doctor';

export default {
    name: 'SelectDoctor',
    data() {
        return {
            // 遮罩层
            loading: false,
            // 选中数组值
            doctorIds: [],
            // 选中的医生数据
            selectedDoctors: [],
            // 初始已选择的医生（从外部传入）
            initialSelectedDoctors: [],
            // 总条数
            total: 0,
            // 医生数据
            doctorList: [],
            // 弹出层标题
            visible: false,
            // 查询参数
            queryParams: {
                pageNum: 1,
                pageSize: 10, // 设置为1000以获取所有医生记录
                name: undefined,
                hospName: undefined,
            },
        };
    },
    methods: {
        // 显示弹框
        show(currentSelectedDoctors = []) {
            console.log('selectDoctor.show() called with:', currentSelectedDoctors);
            console.log('currentSelectedDoctors length:', currentSelectedDoctors.length);

            // 确保传入的数据是有效的数组，并且每个元素都有必要的属性
            const validDoctors = Array.isArray(currentSelectedDoctors) ? currentSelectedDoctors.filter((doctor) => doctor && doctor.id) : [];

            // 保存初始已选择的医生
            this.initialSelectedDoctors = [...validDoctors];
            this.selectedDoctors = [...validDoctors];
            this.doctorIds = validDoctors.map((doctor) => doctor.id);

            console.log('After setting, this.selectedDoctors:', this.selectedDoctors);
            console.log('After setting, this.doctorIds:', this.doctorIds);

            // 重置查询参数到第一页
            this.queryParams.pageNum = 1;
            this.queryParams.name = undefined;
            this.queryParams.hospName = undefined;

            this.getList();
            this.visible = true;
        },
        // 隐藏弹框
        cancel() {
            this.visible = false;
            // 取消时重置数据
            this.reset();
        },
        // 重置
        reset() {
            this.doctorIds = [];
            this.selectedDoctors = [];
            this.initialSelectedDoctors = [];

            this.queryParams = {
                pageNum: 1,
                pageSize: 10,
                name: undefined,
                hospName: undefined,
            };
            this.resetForm('queryForm');
        },
        // 点击行选择
        clickRow(row) {
            this.$refs.table.toggleRowSelection(row);
        },
        // 多选框选中数据
        handleSelectionChange(selection) {
            console.log('handleSelectionChange called with selection:', selection);
            console.log('Current this.selectedDoctors before change:', this.selectedDoctors);

            // 获取当前页面中所有医生的ID
            const currentPageDoctorIds = this.doctorList.map((doctor) => doctor.id);
            console.log('currentPageDoctorIds:', currentPageDoctorIds);

            // 过滤出不在当前页面的已选择医生（来自其他页面的选择）
            const doctorsFromOtherPages = this.selectedDoctors.filter((doctor) => !currentPageDoctorIds.includes(doctor.id));
            console.log('doctorsFromOtherPages:', doctorsFromOtherPages);

            // 确保selection中的每个医生都有完整的信息
            const validSelection = selection.filter((doctor) => doctor && doctor.id);

            // 合并：其他页面的医生 + 当前页面选中的医生
            this.selectedDoctors = [...doctorsFromOtherPages, ...validSelection];
            this.doctorIds = this.selectedDoctors.map((item) => item.id);

            console.log('After merge, this.selectedDoctors:', this.selectedDoctors);
            console.log('After merge, this.doctorIds:', this.doctorIds);
        },
        // 查询表数据
        getList() {
            this.loading = true;
            listDoctor(this.queryParams)
                .then((res) => {
                    this.doctorList = res.data.list || [];
                    this.total = res.data.total || 0;
                    this.loading = false;
                    // 数据加载完成后恢复选中状态
                    this.setSelectedRows();
                })
                .catch(() => {
                    this.loading = false;
                });
        },
        /** 搜索按钮操作 */
        handleQuery() {
            this.queryParams.pageNum = 1;
            this.getList();
        },
        /** 重置按钮操作 */
        resetQuery() {
            this.resetForm('queryForm');
            this.handleQuery();
        },
        // 确定选择医生
        handleSelectDoctor() {
            console.log('handleSelectDoctor called, selectedDoctors:', this.selectedDoctors);
            console.log('selectedDoctors length:', this.selectedDoctors.length);
            console.log('initialSelectedDoctors:', this.initialSelectedDoctors);

            // 使用累积合并逻辑：将初始选择与当前新增选择合并去重
            const mergedDoctors = this.cumulativeSelectDoctors(this.initialSelectedDoctors, this.selectedDoctors);
            console.log('Cumulative merged doctors result:', mergedDoctors);
            console.log('Cumulative merged doctors length:', mergedDoctors.length);

            this.$emit('confirm', mergedDoctors);
            // 确认选择后只关闭对话框，不重置数据
            this.visible = false;
        },
        // 设置表格选中状态
        setSelectedRows() {
            this.$nextTick(() => {
                if (this.$refs.table && this.doctorList.length > 0) {
                    console.log('Setting selected rows for doctorIds:', this.doctorIds);
                    console.log(
                        'Current doctorList:',
                        this.doctorList.map((d) => ({ id: d.id, name: d.name }))
                    );

                    // 清除所有选中状态
                    this.$refs.table.clearSelection();

                    if (this.doctorIds.length > 0) {
                        // 根据已选择的医生ID设置选中状态
                        this.doctorList.forEach((doctor) => {
                            if (this.doctorIds.includes(doctor.id)) {
                                console.log('Selecting doctor:', doctor.name, 'ID:', doctor.id);
                                this.$refs.table.toggleRowSelection(doctor, true);
                            }
                        });
                    }

                    // 验证选中状态是否正确设置
                    this.$nextTick(() => {
                        const selectedRows = this.$refs.table.selection || [];
                        console.log('Verification - Selected rows count:', selectedRows.length);
                        console.log('Verification - Expected count:', this.doctorIds.length);
                    });
                }
            });
        },
        // 累积选择医生：实现纯累积模式，只增加不减少
        cumulativeSelectDoctors(initialDoctors, currentSelectedDoctors) {
            console.log('cumulativeSelectDoctors called');
            console.log('initialDoctors:', initialDoctors);
            console.log('currentSelectedDoctors:', currentSelectedDoctors);

            // 实现纯累积模式：
            // 1. 保留所有初始选择的医生（无论在当前对话框中是否被选中）
            // 2. 添加当前对话框中新选择的医生
            // 3. 去重处理

            const doctorMap = new Map();
            const initialDoctorIds = new Set();

            // 首先添加初始选择的医生（之前确认的选择）
            // 这些医生会被无条件保留
            initialDoctors.forEach((doctor) => {
                if (doctor && doctor.id) {
                    doctorMap.set(doctor.id, doctor);
                    initialDoctorIds.add(doctor.id);
                }
            });

            // 然后添加当前选择的医生中的新医生（不在初始选择中的）
            currentSelectedDoctors.forEach((doctor) => {
                if (doctor && doctor.id && !initialDoctorIds.has(doctor.id)) {
                    // 只添加新选择的医生（不在初始选择中的）
                    doctorMap.set(doctor.id, doctor);
                }
            });

            // 转换为数组并返回
            const cumulativeResult = Array.from(doctorMap.values());
            console.log('Cumulative result:', cumulativeResult);
            console.log('Cumulative result length:', cumulativeResult.length);

            return cumulativeResult;
        },
    },
};
</script>
