<template>
    <div class="app-container">
        <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="100px">
            <el-form-item label="模板名称" prop="name">
                <el-input v-model="queryParams.name" placeholder="请输入模板名称" clearable @keyup.enter.native="handleQuery" />
            </el-form-item>
            <el-form-item label="疾病种类分类" prop="dtId">
                <el-select v-model="queryParams.dtId" placeholder="请选择疾病种类分类" clearable @change="handleQuery">
                    <el-option v-for="type in diseaseTypeList" :key="type.id" :label="type.name" :value="type.id" />
                </el-select>
            </el-form-item>
            <el-form-item>
                <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
                <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
            </el-form-item>
        </el-form>

        <el-row :gutter="10" class="mb8">
            <el-col :span="1.5">
                <el-button type="primary" plain icon="el-icon-plus" size="mini" @click="handleAdd" v-hasPermi="['system:template:add']">新增</el-button>
            </el-col>
            <el-col :span="1.5">
                <el-button type="danger" plain icon="el-icon-delete" size="mini" :disabled="multiple" @click="handleDelete" v-hasPermi="['system:template:remove']">删除</el-button>
            </el-col>
            <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
        </el-row>

        <el-table v-loading="loading" :data="templateList" @selection-change="handleSelectionChange">
            <el-table-column type="selection" width="55" align="center" />
            <el-table-column label="序号" align="center" type="index" width="80" />
            <el-table-column label="模板名称" align="center" prop="name" />
            <el-table-column label="疾病种类分类" align="center" prop="dtName" />
            <el-table-column label="状态" align="center" prop="status">
                <template slot-scope="scope">
                    <el-tag :type="scope.row.status == 1 ? 'success' : 'danger'">
                        {{ scope.row.status == 1 ? '启用' : '停用' }}
                    </el-tag>
                </template>
            </el-table-column>
            <el-table-column label="创建时间" align="center" prop="createTime" width="180">
                <template slot-scope="scope">
                    <span>{{ parseTime(scope.row.createTime, '{y}-{m}-{d} {h}:{i}') }}</span>
                </template>
            </el-table-column>
            <el-table-column label="操作" align="center" class-name="small-padding fixed-width" width="200">
                <template slot-scope="scope">
                    <el-button size="mini" type="text" icon="el-icon-edit" @click="handleUpdate(scope.row)" v-hasPermi="['system:template:edit']">编辑</el-button>
                    <el-button v-if="scope.row.status == 1" size="mini" type="text" icon="el-icon-close" @click="handleStatusChange(scope.row)" v-hasPermi="['system:template:edit']">停用</el-button>
                    <el-button v-else size="mini" type="text" icon="el-icon-check" @click="handleStatusChange(scope.row)" v-hasPermi="['system:template:edit']">启用</el-button>
                    <el-button size="mini" type="text" icon="el-icon-delete" @click="handleDelete(scope.row)" v-hasPermi="['system:template:remove']">删除</el-button>
                </template>
            </el-table-column>
        </el-table>

        <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNum" :limit.sync="queryParams.pageSize" @pagination="getList" />

        <!-- 添加或修改疾病种类模板管理对话框 -->
        <el-dialog :title="title" :visible.sync="open" width="80%" append-to-body>
            <el-form ref="form" :model="form" :rules="dynamicRules" label-width="90px">
                <!-- 第一行：模板名称和疾病种类 -->
                <el-row :gutter="20">
                    <el-col :span="12">
                        <el-form-item label="模板名称" prop="name">
                            <el-input v-model="form.name" placeholder="请输入模板名称" />
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="疾病种类" prop="dtId">
                            <el-select v-model="form.dtId" placeholder="请选择疾病种类" style="width: 100%" filterable>
                                <el-option v-for="item in diseaseTypeList" :key="item.id" :label="item.name" :value="item.id" />
                            </el-select>
                        </el-form-item>
                    </el-col>
                </el-row>

                <!-- 动态字段区域 -->
                <div class="dynamic-fields">
                    <div v-for="(field, index) in form.fields" :key="index" class="field-row">
                        <!-- 字段基本信息行 -->
                        <el-row :gutter="10" class="field-basic-row">
                            <el-col :span="6">
                                <el-form-item label="标签名" :prop="`fields.${index}.name`">
                                    <el-input v-model="field.name" placeholder="请输入标签名" />
                                </el-form-item>
                            </el-col>
                            <el-col :span="6">
                                <el-form-item label="表单类型" :prop="`fields.${index}.type`">
                                    <el-select v-model="field.type" placeholder="请选择表单类型" style="width: 100%" @change="handleFieldTypeChange(index)">
                                        <el-option v-for="dict in dict.type.disease_form_type" :key="dict.value" :label="dict.label" :value="dict.value" />
                                    </el-select>
                                </el-form-item>
                            </el-col>
                            <el-col :span="5">
                                <el-form-item label="排序" :prop="`fields.${index}.sort`">
                                    <el-input-number v-model="field.sort" :min="1" placeholder="排序" style="width: 100%" />
                                </el-form-item>
                            </el-col>
                            <el-col :span="4">
                                <el-form-item label="是否必填" :prop="`fields.${index}.required`">
                                    <el-radio-group v-model="field.required">
                                        <el-radio :label="1">是</el-radio>
                                        <el-radio :label="0">否</el-radio>
                                    </el-radio-group>
                                </el-form-item>
                            </el-col>
                            <el-col :span="2">
                                <el-form-item label="操作">
                                    <el-button type="danger" icon="el-icon-minus" circle @click="removeField(index)" :disabled="form.fields.length === 1"></el-button>
                                </el-form-item>
                            </el-col>
                        </el-row>

                        <!-- 选项管理区域（仅当类型为单选或多选时显示） -->
                        <div v-if="field.type === '3' || field.type === '4'" class="field-options">
                            <el-row :gutter="10">
                                <el-col :span="22" :offset="1">
                                    <div class="options-container">
                                        <div class="options-header">选项管理：</div>
                                        <div v-for="(option, optIndex) in field.options" :key="optIndex" class="option-item">
                                            <el-input v-model="option.label" placeholder="请输入选项名称" style="width: 200px; margin-right: 10px" />
                                            <el-button type="primary" icon="el-icon-plus" circle size="mini" @click="addFieldOption(index)" style="margin-right: 5px"></el-button>
                                            <el-button type="danger" icon="el-icon-minus" circle size="mini" @click="removeFieldOption(index, optIndex)" :disabled="optIndex === 0"></el-button>
                                        </div>
                                    </div>
                                </el-col>
                            </el-row>
                        </div>
                    </div>

                    <!-- 添加字段按钮 -->
                    <el-row>
                        <el-col :span="24">
                            <el-button type="primary" icon="el-icon-plus" @click="addField">添加</el-button>
                        </el-col>
                    </el-row>
                </div>
            </el-form>
            <div slot="footer" class="dialog-footer">
                <el-button type="primary" @click="submitForm">确 定</el-button>
                <el-button @click="cancel">取 消</el-button>
            </div>
        </el-dialog>
    </div>
</template>

<script>
import { listTemplate, getTemplate, delTemplate, addTemplate, updateTemplate, updateTemplateStatus } from '@/api/system/template';
import { listType } from '@/api/system/type';

export default {
    name: 'Template',
    dicts: ['disease_form_type', 'sys_normal_disable'],
    data() {
        return {
            // 遮罩层
            loading: true,
            // 选中数组
            ids: [],
            // 非单个禁用
            single: true,
            // 非多个禁用
            multiple: true,
            // 显示搜索条件
            showSearch: true,
            // 总条数
            total: 0,
            // 疾病种类模板管理表格数据
            templateList: [],
            // 疾病种类列表
            diseaseTypeList: [],
            // 弹出层标题
            title: '',
            // 是否显示弹出层
            open: false,
            // 查询参数
            queryParams: {
                pageNum: 1,
                pageSize: 10,
                dtId: null,
                name: null,
            },
            // 表单参数
            form: {
                id: null,
                dtId: null,
                name: null,
                fields: [
                    {
                        name: '',
                        type: '',
                        sort: 1,
                        required: 1,
                        options: [],
                    },
                ],
            },
            // 表单校验
            rules: {
                dtId: [{ required: true, message: '疾病种类不能为空', trigger: 'change' }],
                name: [{ required: true, message: '模板名称不能为空', trigger: 'blur' }],
            },
        };
    },
    computed: {
        // 动态生成字段验证规则
        dynamicRules() {
            const rules = {
                dtId: [{ required: true, message: '疾病种类不能为空', trigger: 'change' }],
                name: [{ required: true, message: '模板名称不能为空', trigger: 'blur' }],
            };

            // 安全检查：确保 form.fields 存在且为数组
            if (this.form && this.form.fields && Array.isArray(this.form.fields)) {
                // 为每个字段添加验证规则
                this.form.fields.forEach((_, index) => {
                    rules[`fields.${index}.name`] = [{ required: true, message: '标签名不能为空', trigger: 'blur' }];
                    rules[`fields.${index}.type`] = [{ required: true, message: '表单类型不能为空', trigger: 'change' }];
                    rules[`fields.${index}.sort`] = [{ required: true, message: '排序不能为空', trigger: 'blur' }];
                });
            }

            return rules;
        },
    },
    created() {
        this.getList();
        this.getDiseaseTypeList();
    },
    mounted() {
        // 调试：打印数据字典值
        this.$nextTick(() => {
            console.log('disease_form_type 字典值:', this.dict.type.disease_form_type);
        });
    },
    methods: {
        /** 查询疾病种类模板管理列表 */
        getList() {
            this.loading = true;
            listTemplate(this.queryParams).then((response) => {
                this.templateList = response.rows;
                this.total = response.total;
                this.loading = false;
            });
        },
        // 取消按钮
        cancel() {
            this.open = false;
            this.reset();
        },
        // 表单重置
        reset() {
            this.form = {
                id: null,
                dtId: null,
                name: null,
                fields: [
                    {
                        name: '',
                        type: '',
                        sort: 1,
                        required: true,
                        options: [],
                    },
                ],
            };
            this.resetForm('form');
        },
        /** 将接口数据映射为前端表单数据 */
        mapApiDataToForm(apiData) {
            const formData = {
                id: apiData.id,
                dtId: apiData.dtId,
                name: apiData.name,
                fields: [],
            };

            // 映射字段数据
            if (apiData.diseasesTypeTemplateDtlList && apiData.diseasesTypeTemplateDtlList.length > 0) {
                formData.fields = apiData.diseasesTypeTemplateDtlList.map((item) => {
                    const field = {
                        name: item.labelName || '',
                        type: item.formType || '',
                        sort: item.sort || 1,
                        required: item.notNull, // 数值转布尔值
                        options: [],
                    };

                    // 处理选项数据：仅当表单类型为单选(3)或多选(4)时
                    if ((item.formType === '3' || item.formType === '4') && item.items) {
                        field.options = item.items.split(';').map((optionLabel) => ({
                            label: optionLabel.trim(),
                        }));
                    }

                    return field;
                });
            } else {
                // 如果没有字段数据，保持默认的一个空字段
                formData.fields = [
                    {
                        name: '',
                        type: '',
                        sort: 1,
                        required: true,
                        options: [],
                    },
                ];
            }

            return formData;
        },
        /** 搜索按钮操作 */
        handleQuery() {
            this.queryParams.pageNum = 1;
            this.getList();
        },
        /** 重置按钮操作 */
        resetQuery() {
            this.resetForm('queryForm');
            this.handleQuery();
        },
        // 多选框选中数据
        handleSelectionChange(selection) {
            this.ids = selection.map((item) => item.id);
            this.single = selection.length !== 1;
            this.multiple = !selection.length;
        },
        /** 新增按钮操作 */
        handleAdd() {
            this.reset();
            this.open = true;
            this.title = '添加疾病种类模板管理';
        },
        /** 修改按钮操作 */
        handleUpdate(row) {
            this.reset();
            const id = row.id || this.ids;
            getTemplate(id).then((response) => {
                // 使用数据映射方法转换接口数据
                this.form = this.mapApiDataToForm(response.data);
                this.open = true;
                this.title = '修改模板管理';
            });
        },
        /** 提交按钮 */
        submitForm() {
            // 先验证字段数据
            if (!this.validateFields()) {
                return;
            }

            this.$refs['form'].validate((valid) => {
                if (valid) {
                    if (this.form.id != null) {
                        updateTemplate(this.form).then(() => {
                            this.$modal.msgSuccess('修改成功');
                            this.open = false;
                            this.getList();
                        });
                    } else {
                        addTemplate(this.form).then(() => {
                            this.$modal.msgSuccess('新增成功');
                            this.open = false;
                            this.getList();
                        });
                    }
                }
            });
        },
        /** 验证字段数据 */
        validateFields() {
            if (!this.form.fields || this.form.fields.length === 0) {
                this.$modal.msgError('至少需要添加一个字段');
                return false;
            }

            for (let i = 0; i < this.form.fields.length; i++) {
                const field = this.form.fields[i];
                if (!field.name) {
                    this.$modal.msgError(`第${i + 1}行的标签名不能为空`);
                    return false;
                }
                if (!field.type) {
                    this.$modal.msgError(`第${i + 1}行的表单类型不能为空`);
                    return false;
                }
                if (!field.sort) {
                    this.$modal.msgError(`第${i + 1}行的排序不能为空`);
                    return false;
                }

                // 验证单选/多选的选项
                if ((field.type === '3' || field.type === '4') && (!field.options || field.options.length === 0)) {
                    this.$modal.msgError(`第${i + 1}行的单选/多选类型需要添加选项`);
                    return false;
                }

                if (field.options && field.options.length > 0) {
                    for (let j = 0; j < field.options.length; j++) {
                        if (!field.options[j].label) {
                            this.$modal.msgError(`第${i + 1}行的第${j + 1}个选项名称不能为空`);
                            return false;
                        }
                    }
                }
            }

            return true;
        },
        /** 状态切换操作 */
        handleStatusChange(row) {
            const newStatus = row.status === 1 ? 0 : 1;
            const statusText = newStatus === 1 ? '启用' : '停用';

            this.$modal
                .confirm(`是否确认${statusText}模板"${row.name}"？`)
                .then(() => {
                    return updateTemplateStatus({
                        id: row.id,
                        status: newStatus,
                    });
                })
                .then(() => {
                    this.getList();
                    this.$modal.msgSuccess(`${statusText}成功`);
                })
                .catch(() => {});
        },
        /** 删除按钮操作 */
        handleDelete(row) {
            const ids = row.id || this.ids;
            this.$modal
                .confirm('是否确认删除疾病种类模板管理编号为"' + ids + '"的数据项？')
                .then(function () {
                    return delTemplate(ids);
                })
                .then(() => {
                    this.getList();
                    this.$modal.msgSuccess('删除成功');
                })
                .catch(() => {});
        },
        /** 导出按钮操作 */
        handleExport() {
            this.download(
                'system/template/export',
                {
                    ...this.queryParams,
                },
                `template_${new Date().getTime()}.xlsx`
            );
        },
        /** 获取疾病种类列表 */
        getDiseaseTypeList() {
            listType().then((response) => {
                this.diseaseTypeList = response.rows || [];
            });
        },
        /** 字段类型变化处理 */
        handleFieldTypeChange(fieldIndex) {
            const field = this.form.fields[fieldIndex];
            // 当字段类型变化时，处理选项数组
            if (field.type === '3' || field.type === '4') {
                // 单选或多选时，初始化选项数组
                if (!field.options || field.options.length === 0) {
                    this.$set(field, 'options', [{ label: '' }]);
                }
            } else {
                // 其他类型时，清空选项数组
                this.$set(field, 'options', []);
            }
        },
        /** 添加字段 */
        addField() {
            const newSort = this.form.fields.length > 0 ? Math.max(...this.form.fields.map((f) => f.sort || 0)) + 1 : 1;
            this.form.fields.push({
                name: '',
                type: '',
                sort: newSort,
                required: 1,
                options: [],
            });
        },
        /** 移除字段 */
        removeField(index) {
            if (this.form.fields.length > 1) {
                this.form.fields.splice(index, 1);
            }
        },
        /** 添加字段选项 */
        addFieldOption(fieldIndex) {
            const field = this.form.fields[fieldIndex];
            if (!field.options) {
                this.$set(field, 'options', []);
            }
            field.options.push({ label: '' });
        },
        /** 移除字段选项 */
        removeFieldOption(fieldIndex, optionIndex) {
            // 第一个选项不能删除
            if (optionIndex === 0) {
                this.$modal.msgWarning('第一个选项不能删除');
                return;
            }

            const field = this.form.fields[fieldIndex];
            if (field.options && field.options.length > 1) {
                field.options.splice(optionIndex, 1);
            }
        },
    },
};
</script>

<style lang="scss" scoped>
.dynamic-fields {
    .field-row {
        border: 1px solid #e4e7ed;
        border-radius: 4px;
        padding: 15px;
        margin-bottom: 15px;
        background-color: #fafafa;

        .field-basic-row {
            margin-bottom: 10px;
        }

        .field-options {
            margin-top: 15px;
            padding-top: 15px;
            border-top: 1px solid #e4e7ed;

            .options-container {
                background-color: #f9f9f9;
                border: 1px solid #e4e7ed;
                border-radius: 4px;
                padding: 10px;

                .options-header {
                    font-weight: bold;
                    margin-bottom: 10px;
                    color: #606266;
                }

                .option-item {
                    display: flex;
                    align-items: center;
                    margin-bottom: 8px;

                    &:last-child {
                        margin-bottom: 10px;
                    }
                }
            }
        }
    }
}
</style>
