import request from '@/utils/request';

// 查询专家组管理列表
export function listGroup(query) {
    return request({
        url: '/system/group/list',
        method: 'get',
        params: query,
    });
}

// 查询专家组管理详细
export function getGroup(id) {
    return request({
        url: '/system/group/' + id,
        method: 'get',
    });
}

// 新增专家组管理
export function addGroup(data) {
    return request({
        url: '/system/group',
        method: 'post',
        data: data,
    });
}

// 修改专家组管理
export function updateGroup(data) {
    return request({
        url: '/system/group',
        method: 'put',
        data: data,
    });
}
export function updateGroupStatus(data) {
    return request({
        url: '/system/group/status',
        method: 'put',
        data: data,
    });
}

// 删除专家组管理
export function delGroup(id) {
    return request({
        url: '/system/group/' + id,
        method: 'delete',
    });
}

// 专家组状态修改
export function changeGroupStatus(id, status) {
    const data = {
        id,
        status,
    };
    return request({
        url: '/system/group/changeStatus',
        method: 'put',
        data: data,
    });
}
