/**
 * 专家查看相关工具函数
 * 用于处理专家数据获取、格式化等业务逻辑
 */

/**
 * 获取专家列表数据
 * @param {Object} consultationData - 会诊数据
 * @returns {Promise<Array>} 专家列表
 */
export function getExpertList(consultationData) {
  return new Promise((resolve) => {
    // 模拟API调用，实际项目中应该调用真实的API
    setTimeout(() => {
      const mockExpertList = [
        {
          id: 1,
          expertName: "李夏婷",
          status: "通过",
          rejectReason: "",
        },
        {
          id: 2,
          expertName: "张医生",
          status: "待审核",
          rejectReason: "",
        },
        {
          id: 3,
          expertName: "王专家",
          status: "拒绝",
          rejectReason: "时间冲突",
        },
      ];
      resolve(mockExpertList);
    }, 300);
  });
}

/**
 * 格式化专家状态显示
 * @param {string} status - 状态值
 * @returns {Object} 格式化后的状态对象
 */
export function formatExpertStatus(status) {
  const statusMap = {
    '通过': { text: '通过', type: 'success' },
    '待审核': { text: '待审核', type: 'warning' },
    '拒绝': { text: '拒绝', type: 'danger' },
  };
  return statusMap[status] || { text: status, type: 'info' };
}

/**
 * 验证专家数据
 * @param {Object} expertData - 专家数据
 * @returns {boolean} 验证结果
 */
export function validateExpertData(expertData) {
  return expertData && expertData.expertName && expertData.status;
}
