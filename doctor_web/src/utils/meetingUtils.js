/**
 * 会议管理相关工具函数
 */

/**
 * 格式化会议类型
 * @param {string|number} type - 会议类型
 * @returns {string} 格式化后的类型文本
 */
export function formatMeetingType(type) {
  const typeMap = {
    0: "线上",
    1: "线下",
    online: "线上",
    offline: "线下",
  };
  return typeMap[type] || "未知";
}

/**
 * 格式化会议状态
 * @param {string|number} status - 会议状态
 * @returns {Object} 格式化后的状态对象
 */
export function formatMeetingStatus(status) {
  const statusMap = {
    0: { text: "待审核", type: "warning" },
    1: { text: "已审核", type: "success" },
    2: { text: "已完成", type: "info" },
    3: { text: "已取消", type: "danger" },
  };
  return statusMap[status] || { text: "未知", type: "info" };
}

/**
 * 验证会议表单数据
 * @param {Object} formData - 表单数据
 * @returns {Object} 验证结果
 */
export function validateMeetingForm(formData) {
  const errors = [];

  // 验证选择医生
  if (!formData.selectedDoctors || formData.selectedDoctors.length === 0) {
    errors.push("请选择医生");
  }

  // 验证会议类型
  if (!formData.meetingType) {
    errors.push("请选择会议类型");
  }

  // 验证预期开始时间
  if (!formData.expectedStartTime) {
    errors.push("请选择预期会议开始时间");
  }

  // 验证预期结束时间
  if (!formData.expectedEndTime) {
    errors.push("请选择预期会议结束时间");
  }

  // 验证时间逻辑
  if (formData.expectedStartTime && formData.expectedEndTime) {
    if (new Date(formData.expectedStartTime) >= new Date(formData.expectedEndTime)) {
      errors.push("预期结束时间必须晚于开始时间");
    }
  }

  // 验证会议地址/链接
  if (!formData.meetingAddress) {
    errors.push("请输入会议地址或链接");
  }

  return {
    isValid: errors.length === 0,
    errors,
  };
}

/**
 * 格式化日期时间
 * @param {string|Date} datetime - 日期时间
 * @param {string} format - 格式化模板
 * @returns {string} 格式化后的日期时间
 */
export function formatDateTime(datetime, format = "YYYY-MM-DD HH:mm:ss") {
  if (!datetime) return "";

  const date = new Date(datetime);
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, "0");
  const day = String(date.getDate()).padStart(2, "0");
  const hours = String(date.getHours()).padStart(2, "0");
  const minutes = String(date.getMinutes()).padStart(2, "0");
  const seconds = String(date.getSeconds()).padStart(2, "0");

  return format
    .replace("YYYY", year)
    .replace("MM", month)
    .replace("DD", day)
    .replace("HH", hours)
    .replace("mm", minutes)
    .replace("ss", seconds);
}

/**
 * 生成会议编号
 * @returns {string} 会议编号
 */
export function generateMeetingNumber() {
  const now = new Date();
  const year = now.getFullYear();
  const month = String(now.getMonth() + 1).padStart(2, "0");
  const day = String(now.getDate()).padStart(2, "0");
  const random = Math.floor(Math.random() * 10000)
    .toString()
    .padStart(4, "0");

  return `MT${year}${month}${day}${random}`;
}

/**
 * 检查文件类型
 * @param {File} file - 文件对象
 * @param {Array} allowedTypes - 允许的文件类型
 * @returns {boolean} 是否为允许的文件类型
 */
export function checkFileType(file, allowedTypes = ["pdf", "doc", "docx", "jpg", "jpeg", "png"]) {
  if (!file) return false;

  const fileName = file.name.toLowerCase();
  const fileExtension = fileName.split(".").pop();

  return allowedTypes.includes(fileExtension);
}

/**
 * 检查文件大小
 * @param {File} file - 文件对象
 * @param {number} maxSize - 最大文件大小（MB）
 * @returns {boolean} 文件大小是否符合要求
 */
export function checkFileSize(file, maxSize = 10) {
  if (!file) return false;

  const fileSizeMB = file.size / (1024 * 1024);
  return fileSizeMB <= maxSize;
}
