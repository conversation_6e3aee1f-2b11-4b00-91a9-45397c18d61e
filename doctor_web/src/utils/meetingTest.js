/**
 * 会议管理系统测试工具
 * 用于检查组件和功能是否正常工作
 */

// 测试组件导入
export function testComponentImports() {
  const results = {
    success: [],
    errors: []
  }

  try {
    // 测试 API 导入
    import('@/api/system/meeting').then(() => {
      results.success.push('API模块导入成功')
    }).catch(err => {
      results.errors.push(`API模块导入失败: ${err.message}`)
    })

    // 测试工具函数导入
    import('@/utils/meetingUtils').then(() => {
      results.success.push('工具函数导入成功')
    }).catch(err => {
      results.errors.push(`工具函数导入失败: ${err.message}`)
    })

    // 测试组件导入
    import('@/components/Meeting/PatientInfoCard').then(() => {
      results.success.push('PatientInfoCard组件导入成功')
    }).catch(err => {
      results.errors.push(`PatientInfoCard组件导入失败: ${err.message}`)
    })

    import('@/components/Meeting/MeetingInfoForm').then(() => {
      results.success.push('MeetingInfoForm组件导入成功')
    }).catch(err => {
      results.errors.push(`MeetingInfoForm组件导入失败: ${err.message}`)
    })

    import('@/components/Meeting/SupplementInfoForm').then(() => {
      results.success.push('SupplementInfoForm组件导入成功')
    }).catch(err => {
      results.errors.push(`SupplementInfoForm组件导入失败: ${err.message}`)
    })

  } catch (error) {
    results.errors.push(`测试过程中发生错误: ${error.message}`)
  }

  return results
}

// 测试路由配置
export function testRoutes() {
  const routes = [
    '/system/meeting/create/index',
    '/system/meeting/view/index/1',
    '/system/meeting/supplement/index/1'
  ]

  return routes.map(route => ({
    route,
    status: 'pending' // 需要在浏览器中手动测试
  }))
}

// 测试表单验证
export function testFormValidation() {
  try {
    const { validateMeetingForm } = require('@/utils/meetingUtils')
    
    // 测试空表单
    const emptyForm = {}
    const emptyResult = validateMeetingForm(emptyForm)
    
    // 测试完整表单
    const validForm = {
      selectedExpert: '李明照',
      meetingType: '1',
      expectedStartTime: '2023-12-01 10:00:00',
      expectedEndTime: '2023-12-01 12:00:00',
      meetingAddress: '测试地址'
    }
    const validResult = validateMeetingForm(validForm)

    return {
      emptyFormValid: emptyResult.isValid,
      emptyFormErrors: emptyResult.errors,
      validFormValid: validResult.isValid,
      validFormErrors: validResult.errors
    }
  } catch (error) {
    return {
      error: `表单验证测试失败: ${error.message}`
    }
  }
}

// 生成测试报告
export function generateTestReport() {
  const report = {
    timestamp: new Date().toISOString(),
    components: testComponentImports(),
    routes: testRoutes(),
    validation: testFormValidation()
  }

  console.log('=== 会议管理系统测试报告 ===')
  console.log('时间:', report.timestamp)
  console.log('组件测试:', report.components)
  console.log('路由测试:', report.routes)
  console.log('验证测试:', report.validation)
  console.log('========================')

  return report
}

// 在控制台中运行测试的便捷函数
export function runTests() {
  setTimeout(() => {
    generateTestReport()
  }, 1000) // 延迟1秒确保模块加载完成
}
