import request from "@/utils/request";

// 获取患者基础信息（根据接口文档）
export function getMemBaseInfo(id) {
  return request({
    url: "/meeting/getMemBaseInfo",
    method: "get",
    params: { id },
  });
}

// 查询患者列表（支持分页和搜索）
export function listPatients(query) {
  return request({
    url: "/patient/list",
    method: "get",
    params: query,
  });
}

// 查询患者详细信息
export function getPatientDetail(id) {
  return request({
    url: "/patient/" + id,
    method: "get",
  });
}

// 患者搜索功能
export function searchPatients(keyword) {
  return request({
    url: "/patient/search",
    method: "get",
    params: { keyword },
  });
}

// 根据患者ID和类型查询患者信息
export function getPatientByIdAndType(query) {
  return request({
    url: "/patient/getByIdAndType",
    method: "get",
    params: query,
  });
}

// 获取患者基本信息（兼容原有接口）
export function getPatientInfo(patientId) {
  return request({
    url: "/patient/" + patientId,
    method: "get",
  });
}

// 查询医生客户端患者列表
export function listDoctorClientPatients(query) {
  return request({
    url: "/doctorClient/pageBean",
    method: "get",
    params: query,
  });
}

// 查询医生客户端患者详情
export function getDoctorClientPatient(id) {
  return request({
    url: "/doctorClient/patient/" + id,
    method: "get",
  });
}

// 新增患者信息
export function addPatient(data) {
  return request({
    url: "/patient",
    method: "post",
    data: data,
  });
}

// 修改患者信息
export function updatePatient(data) {
  return request({
    url: "/patient",
    method: "put",
    data: data,
  });
}

// 删除患者信息
export function delPatient(id) {
  return request({
    url: "/patient/" + id,
    method: "delete",
  });
}

// 批量删除患者信息
export function delPatients(ids) {
  return request({
    url: "/patient/" + ids,
    method: "delete",
  });
}

// 导出患者信息
export function exportPatients(query) {
  return request({
    url: "/patient/export",
    method: "get",
    params: query,
  });
}

// 获取患者统计信息
export function getPatientStats() {
  return request({
    url: "/patient/stats",
    method: "get",
  });
}

// 根据条件查询患者数量
export function getPatientCount(query) {
  return request({
    url: "/patient/count",
    method: "get",
    params: query,
  });
}
