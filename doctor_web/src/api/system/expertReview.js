import request from "@/utils/request";

// 查询专家评审列表
export function listExpertReview(query) {
  return request({
    url: "/meeting/doctorMeetingList",
    method: "get",
    params: query,
  });
}

// 查询专家评审详细
export function getExpertReview(id) {
  return request({
    url: "/system/expertReview/" + id,
    method: "get",
  });
}

// 新增专家评审
export function addExpertReview(data) {
  return request({
    url: "/system/expertReview",
    method: "post",
    data: data,
  });
}

// 修改专家评审
export function updateExpertReview(data) {
  return request({
    url: "/system/expertReview",
    method: "put",
    data: data,
  });
}

// 删除专家评审
export function delExpertReview(id) {
  return request({
    url: "/system/expertReview/" + id,
    method: "delete",
  });
}

// 审核会诊申请
export function auditMeeting(data) {
  return request({
    url: "meetingDoctor/audit",
    method: "post",
    data: data,
  });
}

// 批量审核会诊申请
export function batchAuditMeeting(data) {
  return request({
    url: "/system/expertReview/audit/batch",
    method: "put",
    data: data,
  });
}

// 查询专家列表
export function listExperts(query) {
  return request({
    url: "/system/expert/list",
    method: "get",
    params: query,
  });
}

// 获取专家详情
export function getExpert(id) {
  return request({
    url: "/system/expert/" + id,
    method: "get",
  });
}
