import request from "@/utils/request";

// 查询会诊列表
export function listConsultation(query) {
  return request({
    url: "/meeting/list",
    method: "get",
    params: query,
  });
}

// 查询会诊详细信息
export function getConsultation(id) {
  return request({
    url: "/meeting/" + id,
    method: "get",
  });
}

// 新增会诊记录
export function addConsultation(data) {
  return request({
    url: "/meeting",
    method: "post",
    data: data,
  });
}

// 修改会诊记录
export function updateConsultation(data) {
  return request({
    url: "/meeting",
    method: "put",
    data: data,
  });
}

// 删除会诊记录
export function delConsultation(id) {
  return request({
    url: "/meeting/" + id,
    method: "delete",
  });
}
