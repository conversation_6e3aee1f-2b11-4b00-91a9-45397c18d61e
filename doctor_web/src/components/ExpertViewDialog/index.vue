<template>
  <el-dialog
    title="查看专家"
    :visible.sync="dialogVisible"
    width="800px"
    :close-on-click-modal="true"
    :close-on-press-escape="true"
    append-to-body
    @close="handleClose"
  >
    <el-table :data="expertList" class="expert-table" border v-loading="loading">
      <el-table-column label="序号" align="center" width="80">
        <template slot-scope="scope">
          <span>{{ scope.$index + 1 }}</span>
        </template>
      </el-table-column>
      <el-table-column label="专家名称" align="center" prop="doctorName" />
      <el-table-column label="状态" align="center" prop="audit">
        <template slot-scope="scope">
          <el-tag :type="getStatusType(scope.row.audit)">
            {{ formatAuditStatus(scope.row.audit) }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="审核时间" align="center" prop="auditTime" width="160">
        <template slot-scope="scope">
          <span>{{ formatDateTime(scope.row.auditTime) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="驳回原因" align="center" prop="reason">
        <template slot-scope="scope">
          <span>{{ scope.row.reason || "-" }}</span>
        </template>
      </el-table-column>
    </el-table>

    <div slot="footer" class="dialog-footer">
      <el-button @click="handleClose">关闭</el-button>
    </div>
  </el-dialog>
</template>

<script>
import { getMeetingExperts } from "@/api/system/meeting";

export default {
  name: "ExpertViewDialog",
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
    consultationData: {
      type: Object,
      default: () => ({}),
    },
    meetingId: {
      type: [String, Number],
      default: null,
    },
  },
  data() {
    return {
      loading: false,
      expertList: [],
    };
  },
  computed: {
    dialogVisible: {
      get() {
        return this.visible;
      },
      set(val) {
        this.$emit("update:visible", val);
      },
    },
  },
  watch: {
    visible(newVal) {
      if (newVal) {
        this.loadExpertList();
      }
    },
  },
  methods: {
    /**
     * 加载专家列表数据
     */
    async loadExpertList() {
      // 检查是否有会议ID
      const currentMeetingId = this.meetingId || this.consultationData?.id;
      if (!currentMeetingId) {
        this.$message.warning("会议ID不能为空");
        return;
      }

      this.loading = true;
      try {
        const response = await getMeetingExperts(currentMeetingId);
        this.expertList = response.rows || response.data || [];
      } catch (error) {
        console.error("获取专家列表失败:", error);
        this.$message.error("获取专家列表失败");
        this.expertList = [];
      } finally {
        this.loading = false;
      }
    },
    /**
     * 获取状态类型
     */
    getStatusType(audit) {
      const statusMap = {
        0: "warning", // 未审核
        1: "success", // 同意
        2: "danger", // 驳回
      };
      return statusMap[audit] || "info";
    },

    /**
     * 格式化审核状态
     */
    formatAuditStatus(audit) {
      const statusMap = {
        0: "未审核",
        1: "同意",
        2: "驳回",
      };
      return statusMap[audit] || "未知";
    },

    /**
     * 格式化日期时间
     */
    formatDateTime(dateTime) {
      if (!dateTime) return "-";

      // 如果是字符串，直接返回（后端已格式化）
      if (typeof dateTime === "string") {
        return dateTime;
      }

      // 如果是Date对象，格式化为 yyyy-MM-dd HH:mm:ss
      if (dateTime instanceof Date) {
        const year = dateTime.getFullYear();
        const month = String(dateTime.getMonth() + 1).padStart(2, "0");
        const day = String(dateTime.getDate()).padStart(2, "0");
        const hours = String(dateTime.getHours()).padStart(2, "0");
        const minutes = String(dateTime.getMinutes()).padStart(2, "0");
        const seconds = String(dateTime.getSeconds()).padStart(2, "0");
        return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
      }

      return "-";
    },
    /**
     * 关闭弹窗
     */
    handleClose() {
      this.dialogVisible = false;
      this.expertList = [];
    },
  },
};
</script>

<style scoped>
/* 专家弹窗样式 */
.expert-table {
  width: 100%;
}

.dialog-footer {
  text-align: center;
  padding: 10px 0;
}
</style>
