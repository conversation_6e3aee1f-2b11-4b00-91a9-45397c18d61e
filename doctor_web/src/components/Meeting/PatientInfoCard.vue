<template>
  <div class="patient-info-card">
    <div class="card-header">
      <h3>基础信息</h3>
    </div>
    <div class="card-content">
      <el-row :gutter="20">
        <el-col :span="8">
          <div class="info-item">
            <label>姓名：</label>
            <span>{{ patientInfo.realName || "-" }}</span>
          </div>
        </el-col>
        <el-col :span="8">
          <div class="info-item">
            <label>性别：</label>
            <span>{{ patientInfo.gender || "-" }}</span>
          </div>
        </el-col>
        <el-col :span="8">
          <div class="info-item">
            <label>年龄：</label>
            <span>{{ patientInfo.age || "-" }}</span>
          </div>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="8">
          <div class="info-item">
            <label>出生日期：</label>
            <span>{{ patientInfo.birthday || "-" }}</span>
          </div>
        </el-col>
        <el-col :span="8">
          <div class="info-item">
            <label>民族：</label>
            <span>{{ patientInfo.nationality || "-" }}</span>
          </div>
        </el-col>
        <el-col :span="8">
          <div class="info-item">
            <label>籍贯：</label>
            <span>{{ patientInfo.nativePlace || "-" }}</span>
          </div>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="8">
          <div class="info-item">
            <label>医院名称：</label>
            <span>{{ patientInfo.hospital || "-" }}</span>
          </div>
        </el-col>
        <el-col :span="8">
          <div class="info-item">
            <label>医生：</label>
            <span>{{ patientInfo.doctor || "-" }}</span>
          </div>
        </el-col>
        <el-col :span="8">
          <div class="info-item">
            <label>疾病种类：</label>
            <span>{{ patientInfo.diseaseType || "-" }}</span>
          </div>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <div class="info-item">
            <label>联系方式：</label>
            <span>{{ patientInfo.mobile || "-" }}</span>
          </div>
        </el-col>
      </el-row>
    </div>
  </div>
</template>

<script>
export default {
  name: "PatientInfoCard",
  props: {
    patientInfo: {
      type: Object,
      default: () => ({}),
    },
  },
};
</script>

<style lang="scss" scoped>
.patient-info-card {
  background: #fff;
  border-radius: 4px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  margin-bottom: 20px;

  .card-header {
    padding: 15px 20px;
    border-bottom: 1px solid #e4e7ed;
    background-color: #f5f7fa;

    h3 {
      margin: 0;
      font-size: 16px;
      font-weight: 600;
      color: #303133;
    }
  }

  .card-content {
    padding: 20px;

    .el-row {
      margin-bottom: 15px;

      &:last-child {
        margin-bottom: 0;
      }
    }

    .info-item {
      display: flex;
      align-items: center;
      margin-bottom: 10px;

      &:last-child {
        margin-bottom: 0;
      }

      label {
        font-weight: 500;
        color: #606266;
        min-width: 80px;
        margin-right: 10px;
      }

      span {
        color: #303133;
        flex: 1;
      }
    }
  }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .patient-info-card {
    .card-content {
      padding: 15px;

      .info-item {
        flex-direction: column;
        align-items: flex-start;

        label {
          margin-bottom: 5px;
          min-width: auto;
        }
      }
    }
  }
}
</style>
