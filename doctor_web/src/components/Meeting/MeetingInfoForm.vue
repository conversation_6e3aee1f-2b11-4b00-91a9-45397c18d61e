<template>
  <div class="meeting-info-form">
    <div class="meeting-form-header">
      <h3>会议信息</h3>
    </div>
    <div class="form-content">
      <el-form :model="formData" :rules="rules" ref="meetingForm" label-width="150px">
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="选择医生：" prop="selectedDoctorIds" :required="!readonly">
              <el-select
                v-model="formData.selectedDoctorIds"
                multiple
                placeholder="请选择医生"
                :disabled="readonly"
                @change="handleDoctorSelectionChange"
                style="width: 100%"
              >
                <el-option
                  v-for="doctor in doctorList"
                  :key="doctor.id"
                  :label="`${doctor.name || doctor.doctorName || '未知医生'} - ${
                    doctor.depName || doctor.deptName || '未知科室'
                  }`"
                  :value="doctor.id"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="会议类型：" prop="meetingType" :required="!readonly">
              <el-radio-group v-model="formData.meetingType" :disabled="readonly">
                <el-radio :label="0">线上</el-radio>
                <el-radio :label="1">线下</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="预期会议开始时间：" prop="expectedStartTime" :required="!readonly">
              <el-date-picker
                v-model="formData.expectedStartTime"
                type="datetime"
                placeholder="选择日期时间"
                format="yyyy-MM-dd HH:mm:ss"
                value-format="yyyy-MM-dd HH:mm:ss"
                style="width: 100%"
                :readonly="readonly"
                :disabled="readonly"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="预期会议结束时间：" prop="expectedEndTime" :required="!readonly">
              <el-date-picker
                v-model="formData.expectedEndTime"
                type="datetime"
                placeholder="选择日期时间"
                format="yyyy-MM-dd HH:mm:ss"
                value-format="yyyy-MM-dd HH:mm:ss"
                style="width: 100%"
                :readonly="readonly"
                :disabled="readonly"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row>
          <el-col :span="24">
            <el-form-item label="会议地址/链接：" prop="meetingAddress" :required="!readonly">
              <el-input
                v-model="formData.meetingAddress"
                type="textarea"
                :rows="3"
                placeholder="请输入会议地址或链接"
                :readonly="readonly"
                :disabled="readonly"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <template v-if="isSupplement || readonlySupplement">
          <el-row>
            <el-col :span="24">
              <el-form-item label="是否通过：" prop="isPass" :required="!readonlySupplement">
                <el-radio-group v-model="formData.isPass" :disabled="readonlySupplement" :readonly="readonlySupplement">
                  <el-radio :label="0">通过</el-radio>
                  <el-radio :label="1">不通过</el-radio>
                </el-radio-group>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="24">
              <el-form-item label="会议记录：" prop="remark" :required="!readonlySupplement">
                <el-input
                  v-model="formData.remark"
                  type="textarea"
                  :disabled="readonlySupplement"
                  :readonly="readonlySupplement"
                  :rows="3"
                  placeholder="请输入会议记录"
                />
              </el-form-item>
            </el-col>
          </el-row>
        </template>
      </el-form>
    </div>
  </div>
</template>

<script>
import { listDoctor } from "@/api/system/doctor";

export default {
  name: "MeetingInfoForm",
  props: {
    value: {
      type: Object,
      default: () => ({}),
    },
    readonly: {
      type: Boolean,
      default: false,
    },
    isSupplement: {
      type: Boolean,
      default: false,
    },
    readonlySupplement: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      rules: {
        selectedDoctorIds: [{ type: "array", required: true, message: "请选择医生", trigger: "change" }],
        meetingType: [{ type: "number", required: true, message: "请选择会议类型", trigger: "change" }],
        expectedStartTime: [{ type: "date", required: true, message: "请选择预期会议开始时间", trigger: "change" }],
        expectedEndTime: [{ type: "date", required: true, message: "请选择预期会议结束时间", trigger: "change" }],
        meetingAddress: [{ type: "string", required: true, message: "请输入会议地址或链接", trigger: "blur" }],
        isPass: [{ type: "number", required: true, message: "请选择是否通过", trigger: "change" }],
        remark: [{ type: "string", required: true, message: "请输入会议记录", trigger: "blur" }],
      },
      doctorList: [],
    };
  },
  computed: {
    formData: {
      get() {
        return this.value;
      },
      set(val) {
        this.$emit("input", val);
      },
    },
  },
  mounted() {
    // 组件挂载时加载医生列表
    this.loadDoctorList();
  },
  methods: {
    // 加载医生列表
    async loadDoctorList() {
      try {
        const response = await listDoctor();
        this.doctorList = response.data.list || [];

        if (this.doctorList.length === 0) {
          this.$message.warning("暂无可选择的医生");
        }
      } catch (error) {
        console.error("获取医生列表失败:", error);
        this.$message.error("获取医生列表失败");
        this.doctorList = [];
      }
    },

    // 处理医生选择变化
    handleDoctorSelectionChange(selectedIds) {
      try {
        // 根据选中的ID找到对应的医生对象
        const selectedDoctors = this.doctorList.filter((doctor) => selectedIds.includes(doctor.id));

        // 智能字段映射 - 支持多种可能的字段名
        this.formData.selectedDoctors = selectedDoctors.map((doctor) => {
          const doctorName = doctor.name || doctor.doctorName || doctor.userName || doctor.realName || "未知医生";
          const deptName =
            doctor.depName || doctor.deptName || doctor.departmentName || doctor.department || "未知科室";
          const jobTitle = doctor.job || doctor.jobTitle || doctor.title || doctor.position || "未知职称";

          return {
            id: doctor.id,
            doctorName: doctorName,
            deptName: deptName,
            jobTitle: jobTitle,
          };
        });
      } catch (error) {
        console.error("处理医生选择时出错:", error);
        this.$message.error("选择医生时出现错误，请重试");
      }
    },

    // 表单验证
    validate() {
      return new Promise((resolve) => {
        this.$refs.meetingForm.validate((valid) => {
          resolve(valid);
        });
      });
    },

    // 重置表单
    resetForm() {
      this.$refs.meetingForm.resetFields();
    },
  },
};
</script>

<style lang="scss" scoped>
.meeting-info-form {
  background: #fff;
  border-radius: 4px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  margin-bottom: 20px;

  .meeting-form-header {
    padding: 15px 20px;
    border-bottom: 1px solid #e4e7ed;
    background-color: #f5f7fa;

    h3 {
      margin: 0;
      font-size: 16px;
      font-weight: 600;
      color: #303133;
    }
  }

  .form-content {
    padding: 20px;
  }
}

.dialog-footer {
  text-align: center;
}

::v-deep .el-radio {
  margin-right: 20px;
}
</style>
