<template>
  <div class="app-container">
    <div class="meeting-supplement-page">
      <div class="page-header">
        <h2>补充会诊</h2>
      </div>

      <div v-loading="loading">
        <!-- 患者基础信息 -->
        <PatientInfoCard :patient-info="meetingData.patientInfo || {}" />

        <!-- 会议信息 -->
        <MeetingInfoForm v-model="meetingData.meetingInfo" ref="meetingForm" :readonly="true" is-supplement />

        <!-- 补充信息 -->
        <SupplementInfoForm v-model="meetingData.values" :readonly="true" :show-tooltip="false" />
      </div>

      <!-- 操作按钮 -->
      <div class="action-buttons">
        <el-button @click="handleCancel">取消</el-button>
        <el-button type="primary" @click="handleConfirm" :loading="submitting">确认</el-button>
      </div>
    </div>
  </div>
</template>

<script>
import PatientInfoCard from "@/components/Meeting/PatientInfoCard";
import MeetingInfoForm from "@/components/Meeting/MeetingInfoForm";
import SupplementInfoForm from "@/components/Meeting/SupplementInfoForm";
import { getMeeting, supplementMeeting } from "@/api/system/meeting";

export default {
  name: "MeetingSupplement",
  components: {
    PatientInfoCard,
    MeetingInfoForm,
    SupplementInfoForm,
  },
  data() {
    return {
      loading: false,
      submitting: false,
      meetingData: {
        patientInfo: {},
        meetingInfo: {
          selectedDoctorIds: [],
          meetingType: 0,
          expectedStartTime: "",
          expectedEndTime: "",
          meetingAddress: "",
          isPass: 0,
          remark: "",
        },
        values: [],
      },
    };
  },
  created() {
    this.loadMeetingData();
  },
  methods: {
    // 加载会议数据
    async loadMeetingData() {
      const meetingId = this.$route.params.id;
      if (!meetingId) {
        this.$message.error("会议ID不能为空");
        this.$router.go(-1);
        return;
      }

      this.loading = true;
      try {
        const response = await getMeeting(meetingId);
        this.meetingData.patientInfo = response.data.webPatientVO;
        this.meetingData.meetingInfo = {
          selectedDoctorIds: response.data.meeting.doctors.map((e) => e.doctorId),
          meetingAddress: response.data.meeting.address,
          meetingType: response.data.meeting.consultType,
          expectedStartTime: response.data.meeting.expectStartTime,
          expectedEndTime: response.data.meeting.expectEndTime,
          isPass: 0,
          remark: "",
        };
        this.meetingData.values = response.data.values || [];
      } catch (error) {
        console.error("获取会议信息失败:", error);
        this.$message.error("获取会议信息失败");
      } finally {
        this.loading = false;
      }
    },

    // 处理取消
    handleCancel() {
      this.$confirm("确认取消补充会诊信息吗？", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          this.$router.go(-1);
        })
        .catch(() => {});
    },

    // 处理确认提交
    async handleConfirm() {
      try {
        // 验证表单
        const meetingFormValid = await this.$refs.meetingForm.validate();

        if (!meetingFormValid) {
          this.$message.error("请完善表单信息");
          return;
        }

        this.submitting = true;

        // 构建提交数据
        const submitData = {
          meetingId: this.$route.params.id,
          isPass: this.meetingData.meetingInfo.isPass,
          remark: this.meetingData.meetingInfo.remark,
        };

        // 提交补充信息
        await supplementMeeting(submitData);

        this.$message.success("补充信息提交成功");

        // 跳转到会议列表或详情页
        this.$router.push("/consultation/index");
      } catch (error) {
        console.error("提交补充信息失败:", error);
      } finally {
        this.submitting = false;
      }
    },
  },
};
</script>

<style lang="scss" scoped>
.meeting-supplement-page {
  max-width: 1200px;
  margin: 0 auto;

  .page-header {
    margin-bottom: 20px;

    h2 {
      margin: 0;
      font-size: 24px;
      font-weight: 600;
      color: #303133;
    }
  }

  .action-buttons {
    text-align: center;
    padding: 20px 0;
    margin-top: 20px;

    .el-button {
      min-width: 100px;
      margin: 0 10px;
    }
  }
}

.app-container {
  padding: 20px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .meeting-supplement-page {
    max-width: 100%;

    .action-buttons {
      .el-button {
        width: 100px;
        margin: 5px;
      }
    }
  }
}
</style>
