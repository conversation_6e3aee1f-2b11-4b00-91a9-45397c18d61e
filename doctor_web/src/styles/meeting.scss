/**
 * 会议管理页面统一样式
 */

/* 基础布局样式 */
.meeting-page {
  .page-header {
    margin-bottom: 20px;
    padding-bottom: 15px;
    border-bottom: 1px solid #e4e7ed;

    h2 {
      margin: 0;
      font-size: 24px;
      font-weight: 600;
      color: #303133;
    }

    .page-subtitle {
      margin-top: 5px;
      font-size: 14px;
      color: #909399;
    }
  }

  .page-content {
    max-width: 1200px;
    margin: 0 auto;
  }

  .action-buttons {
    text-align: center;
    padding: 20px 0;
    border-top: 1px solid #e4e7ed;
    margin-top: 20px;

    .el-button {
      min-width: 100px;
      margin: 0 10px;
    }
  }
}

/* 卡片组件样式 */
.meeting-card {
  background: #fff;
  border-radius: 4px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  margin-bottom: 20px;

  .card-header {
    padding: 15px 20px;
    border-bottom: 1px solid #e4e7ed;
    background-color: #f5f7fa;
    display: flex;
    justify-content: space-between;
    align-items: center;

    h3 {
      margin: 0;
      font-size: 16px;
      font-weight: 600;
      color: #303133;
    }

    .header-extra {
      font-size: 14px;
      color: #909399;
    }
  }

  .card-content {
    padding: 20px;
  }
}

/* 表单样式 */
.meeting-form {
  .el-form-item {
    margin-bottom: 18px;

    .el-form-item__label {
      font-weight: 500;
      color: #606266;
    }

    .el-form-item__content {
      .el-input,
      .el-select,
      .el-date-picker {
        width: 100%;
      }
    }
  }

  .form-section {
    margin-bottom: 30px;

    .section-title {
      font-size: 16px;
      font-weight: 600;
      color: #303133;
      margin-bottom: 15px;
      padding-bottom: 8px;
      border-bottom: 1px solid #e4e7ed;
    }
  }

  .required-mark {
    color: #f56c6c;
    margin-right: 4px;
  }
}

/* 信息展示样式 */
.info-display {
  .info-row {
    display: flex;
    margin-bottom: 15px;

    &:last-child {
      margin-bottom: 0;
    }
  }

  .info-item {
    flex: 1;
    display: flex;
    align-items: center;
    margin-right: 20px;

    &:last-child {
      margin-right: 0;
    }

    .label {
      font-weight: 500;
      color: #606266;
      min-width: 80px;
      margin-right: 10px;
    }

    .value {
      color: #303133;
      flex: 1;
    }
  }
}

/* 文件上传样式 */
.meeting-upload {
  .upload-area {
    border: 2px dashed #d9d9d9;
    border-radius: 6px;
    width: 100px;
    height: 100px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: border-color 0.3s;

    &:hover {
      border-color: #409eff;
    }

    .upload-icon {
      font-size: 28px;
      color: #8c939d;
    }
  }

  .file-list {
    margin-top: 10px;

    .file-item {
      display: flex;
      align-items: center;
      padding: 8px 0;
      border-bottom: 1px solid #f0f0f0;

      &:last-child {
        border-bottom: none;
      }

      .file-icon {
        margin-right: 8px;
        color: #409eff;
      }

      .file-name {
        flex: 1;
        color: #303133;
      }

      .file-actions {
        .el-button {
          padding: 0;
          margin-left: 8px;
        }
      }
    }
  }
}

/* 状态标签样式 */
.status-tag {
  &.status-pending {
    background-color: #fdf6ec;
    color: #e6a23c;
    border: 1px solid #f5dab1;
  }

  &.status-approved {
    background-color: #f0f9ff;
    color: #409eff;
    border: 1px solid #b3d8ff;
  }

  &.status-completed {
    background-color: #f0f9f0;
    color: #67c23a;
    border: 1px solid #c2e7b0;
  }

  &.status-cancelled {
    background-color: #fef0f0;
    color: #f56c6c;
    border: 1px solid #fbc4c4;
  }
}

/* 工具提示样式 */
.meeting-tooltip {
  .tooltip-content {
    background: #ffeb3b;
    padding: 10px;
    border-radius: 4px;
    font-size: 12px;
    line-height: 1.4;
    color: #333;
    max-width: 200px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .meeting-page {
    .page-content {
      max-width: 100%;
      padding: 0 10px;
    }

    .action-buttons {
      .el-button {
        width: 100px;
        margin: 5px;
      }
    }
  }

  .meeting-card {
    .card-content {
      padding: 15px;
    }
  }

  .info-display {
    .info-row {
      flex-direction: column;
    }

    .info-item {
      margin-right: 0;
      margin-bottom: 10px;
      flex-direction: column;
      align-items: flex-start;

      .label {
        margin-bottom: 5px;
        min-width: auto;
      }
    }
  }

  .meeting-form {
    .el-form-item {
      .el-form-item__label {
        line-height: 1.4;
      }
    }
  }
}

/* 打印样式 */
@media print {
  .meeting-page {
    .action-buttons {
      display: none;
    }

    .page-header {
      border-bottom: 2px solid #000;
    }
  }

  .meeting-card {
    box-shadow: none;
    border: 1px solid #000;
    page-break-inside: avoid;
  }
}
